/**
 * @file Interrupt.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 存储的是各种中断相关的
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Interrupt.h"

uint8_t enable_group1_irq = 0;
bool Flag_Serial_RXcplt = false; //接收完成标志位
uint32_t ExISR_Flag; //中断判断标志位
bool Flag_MPU6050_Ready = false; //MPU6050是否准备好

#define ISR_IS_GPIO(X)        (ExISR_Flag & (X))
#define GET_RDR_B_VAL(X)      DL_GPIO_readPins(SPD_READER_B_PORT, (X))
#define CLR_GPIOB_ISR_FLAG(X) DL_GPIO_clearInterruptStatus(GPIOB, (X))

/**
 * @brief Systick时钟中断
 * 
 */
void SysTick_Handler(void)
{
    SysTick_Increasment();
}

/**
 * @brief 串口0中断
 * 
 */
void UART_0_INST_IRQHandler(void)
{
    //DMA接收完成中断
    if (DL_UART_Main_getPendingInterrupt(UART0_INST) == DL_UART_MAIN_IIDX_DMA_DONE_RX)
    {
        if (!DL_UART_isRXFIFOEmpty(UART0_INST))
        {
        }
        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_DMA_DONE_RX);
    }
    //发送完成中断
    if (DL_UART_Main_getPendingInterrupt(UART0_INST) == DL_UART_MAIN_IIDX_EOT_DONE)
    {
        DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
        // LED_BOARD_TOGGLE();
        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_EOT_DONE);
    }
}

/**
 * @brief 外部中断
 * 
 */
void GROUP1_IRQHandler(void)
{
    //判断是不是由GPIOB触发的中断
    if (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) == GPIO_MULTIPLE_GPIOB_INT_IIDX)
    {
        //查看哪个IO进入外部中断
        // ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOA,
        //                                                SPD_READER_A_FONT_LEFT_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN |
        //                                                    SPD_READER_A_BACK_LEFT_A_PIN |
        //                                                    SPD_READER_A_BACK_RIGHT_A_PIN | GPIO_MPU6050_PIN_INT_PIN);

        
        ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOB,
                                                       SPD_READER_A_FONT_LEFT_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN | GPIO_MPU6050_PIN_INT_PIN);

        if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) Data_MotorEncoder[MOTOR_FONT_LEFT]++;
            else Data_MotorEncoder[MOTOR_FONT_LEFT]--;
            CLR_GPIOB_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
        }

        /* 稍作更改 取反 */
        if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) Data_MotorEncoder[MOTOR_FONT_RIGHT]--;
            else Data_MotorEncoder[MOTOR_FONT_RIGHT]++;
            CLR_GPIOB_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
        }

        // if (ISR_IS_GPIO(SPD_READER_A_BACK_LEFT_A_PIN)) //右后轮
        // {
        //     if (GET_RDR_B_VAL(SPD_READER_B_BACK_LEFT_B_PIN)) Data_MotorEncoder[MOTOR_BACK_LEFT]++;
        //     else Data_MotorEncoder[MOTOR_BACK_LEFT]--;
        //     CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_LEFT_A_PIN);
        // }

        // if (ISR_IS_GPIO(SPD_READER_A_BACK_RIGHT_A_PIN)) //右后轮
        // {
        //     if (GET_RDR_B_VAL(SPD_READER_B_BACK_RIGHT_B_PIN)) Data_MotorEncoder[MOTOR_BACK_RIGHT]++;
        //     else Data_MotorEncoder[MOTOR_BACK_RIGHT]--;
        //     CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_RIGHT_A_PIN);
        // }

        //MPU6050读取数据
        if (ISR_IS_GPIO(GPIO_MPU6050_PIN_INT_PIN))
        {
            if (Flag_MPU6050_Ready == false) Flag_MPU6050_Ready = true;
            CLR_GPIOB_ISR_FLAG(GPIO_MPU6050_PIN_INT_PIN);
        }
    }

    /* 读取Group1的中断寄存器并清除中断标志位 */
    // switch (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1)) 
    // {
    //     case SPD_READER_A_FONT_LEFT_A_IIDX:
    //         /*前左轮进入外部中断*/
    //         ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOB,SPD_READER_A_FONT_LEFT_A_PIN);
    //         if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
    //         {
    //             if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) Data_MotorEncoder[MOTOR_FONT_LEFT]++;
    //             else Data_MotorEncoder[MOTOR_FONT_LEFT]--;
    //             CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    //         }
    //     break;

    //     case SPD_READER_A_FONT_RIGHT_A_IIDX:
    //         /*前右轮进入外部中断*/
    //         ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOB,SPD_READER_A_FONT_RIGHT_A_PIN);
    //         if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮
    //         {
    //             if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) Data_MotorEncoder[MOTOR_FONT_RIGHT]++;
    //             else Data_MotorEncoder[MOTOR_FONT_RIGHT]--;
    //             CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
    //         }
    //     break;

    //     case GPIO_MPU6050_PIN_INT_IIDX:
    //         /*陀螺仪进入外部中断*/
    //         ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOB,GPIO_MPU6050_PIN_INT_PIN);
    //         /*MPU6050读取数据*/
    //         if (ISR_IS_GPIO(GPIO_MPU6050_PIN_INT_PIN))
    //         {
    //             if (Flag_MPU6050_Ready == false) Flag_MPU6050_Ready = true;
    //             CLR_GPIOA_ISR_FLAG(GPIO_MPU6050_PIN_INT_PIN);
    //         }
    //     break;

    // }
}

/**
 * @brief 中断初始化
 * 
 */
void Interrupt_Init(void)
{
    NVIC_EnableIRQ(1);

    CLR_GPIOB_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    CLR_GPIOB_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
    // CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_LEFT_A_PIN);
    // CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_RIGHT_A_PIN);
    CLR_GPIOB_ISR_FLAG(GPIO_MPU6050_PIN_INT_IIDX);
}
