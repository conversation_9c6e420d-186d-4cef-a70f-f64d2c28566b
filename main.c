/* 头文件声明区 */
#include "SysConfig.h"

/* 主体函数声明区 */
int main(void)
{
    /* 系统底层驱动出初始化 */
    SYSCFG_DL_init();
    /* 调度器任务初始化 */
    Task_Init();
    while (1)
    {
        /* 调度器任务执行 */
        Task_Start(Sys_GetTick);
        if(Task_Flag == 1)//等待初始化完成。。。
		{
			switch(Task_State)
			{
				case 1://任务1
					// Task_State = Task_1()?5:1;
                    // Task_State = MyPrintf("successr\n")?5:1;
				break;
				
				case 2://任务2
					
				break;
				
				case 3://任务3
					
				break;
				
				case 4://任务4

				break;
					
				case 5://任务结束区

				break;
			}
		}

    }
}
