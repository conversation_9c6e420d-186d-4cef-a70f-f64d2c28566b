#ifndef __MyConfig_h
#define __MyConfig_h

#include "SysConfig.h"

//编码器的物理分辨率，线速
#define RESOLUTION 13

//经过倍频之后的分辨率
#define RESOLUTION_TOTAL 13*4

//减速电机的减速比
#define REDUCTION_RATIO  30

//轮胎半径
#define WHEEL_R  3.25

//车长
#define CAR_LENGTH  25

//自旋旋转90的距离
#define SPIN_90_DISTANCE 0.25*3.14*8.4  //

//两轮间距
#define TWO_WHEEL_DISTANCE  16.8

//电机的最大转速
#define MOTOR_SPEED_MAX  160 //单位160RPM,1s能跑的最大距离53cm

#endif