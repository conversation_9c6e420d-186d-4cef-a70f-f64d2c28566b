#ifndef __MyConfig_h
#define __MyConfig_h

/* 头文件声明区 */
#include "SysConfig.h"

/* 常变量声明区 */

//编码器的物理分辨率，线速
#define RESOLUTION 13

//经过二倍频之后的分辨率
#define RESOLUTION_TOTAL 13*2

//减速电机的减速比 310电机
#define REDUCTION_RATIO  20

//轮胎半径
#define WHEEL_R  3.25

//车长
#define CAR_LENGTH  14.5

//自旋旋转90的距离
#define SPIN_90_DISTANCE 0.5*3.14*5  

//两轮间距
#define TWO_WHEEL_DISTANCE  10

//电机的最大转速
#define MOTOR_SPEED_MAX  160 //单位160RPM,1s能跑的最大距离53cm

//PID的计算周期
#define PID_COMPUTATION_PERIOD  10//单位为ms

#define TOTAL_MOTOR_RESOLUTION (REDUCTION_RATIO*RESOLUTION_TOTAL) 

/* 自定义数据类型区 */
/*
 * Param_InitTypeDef 用于保存小车运动相关的参数
 */
typedef struct
{
    int16_t UnitTime_Motor1Pluse;//单位时间内的电机脉冲数（转速）
	int16_t UnitTime_Motor2Pluse;//单位时间内的电机脉冲数（转速）
	float 	Distance_Motor1Curret;//当前行驶距离
	float 	Distance_Motor2Curret;//当前行驶距离
	float	Distance_TargetThreshold;//目标距离阈值
	uint16_t Motor1_PWM;//左电机PWM数值
	uint16_t Motor2_PWM;//右电机PWM数值
	uint8_t  Send_Step;
	uint8_t  Back_Step;
	int16_t Line_TempOut;//巡线参数输出

}Param_InitTypeDef;

/*
 * Flag_InitTypeDef 用于保存小车各种标志位
 */
typedef struct
{
	uint8_t Is_EnMOTOR;//电机使能
	uint8_t Start_Line;//开始巡线
	uint8_t Stop_Car;//停车
	uint8_t Run_Step;
	uint8_t Start_Back;//开始后退
	uint8_t Success_Spin;//旋转成功
	uint8_t Receive_TargerNum;
	uint8_t Receive_Num_Orientation;
	uint8_t Recognition_Spin_Left;
	uint8_t Pause_Line;//暂停巡线
	uint8_t Recognition_Pause;
}Flag_InitTypeDef;

/* 外部变量声明 */
extern Param_InitTypeDef Param;
extern Flag_InitTypeDef Flag;

extern uint16_t time;         //巡线时间设定专用变量
extern uint16_t Back_time;    //后退停止时间专用变量

extern uint16_t stop_time_cnt;//巡线停止时间
extern uint16_t back_time_cnt;//后退停止时间

#endif