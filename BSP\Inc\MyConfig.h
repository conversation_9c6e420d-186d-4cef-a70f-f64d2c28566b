#ifndef __MyConfig_h
#define __MyConfig_h

/* 头文件声明区 */
#include "SysConfig.h"

/* 常变量声明区 */

//编码器的物理分辨率，线速
#define RESOLUTION 13

//经过二倍频之后的分辨率
#define RESOLUTION_TOTAL 13*2

//减速电机的减速比 310电机
#define REDUCTION_RATIO  20

//轮胎半径
#define WHEEL_R  3.25

//车长
#define CAR_LENGTH  14.5

//自旋旋转90的距离
#define SPIN_90_DISTANCE 0.5*3.14*5  

//两轮间距
#define TWO_WHEEL_DISTANCE  10

//电机的最大转速
#define MOTOR_SPEED_MAX  160 //单位160RPM,1s能跑的最大距离53cm

#define TOTAL_MOTOR_RESOLUTION (RESOLUTION*RESOLUTION_TOTAL) 

/* 自定义数据类型区 */
/*
 * Param_InitTypeDef 用于保存小车运动相关的参数
 */
typedef struct
{
	float 	Distance_Motor1Curret;//当前行驶距离
	float 	Distance_Motor2Curret;//当前行驶距离
	float	Distance_TargetThreshold;//目标距离阈值
	uint16_t Motor1_PWM;//左电机PWM数值
	uint16_t Motor2_PWM;//右电机PWM数值
	uint8_t  Send_Step;
	uint8_t  Back_Step;
	int16_t Line_TempOut;//巡线参数输出

}Param_InitTypeDef;

/* 外部变量声明 */
extern Param_InitTypeDef Param;

extern uint16_t time;         //巡线时间设定专用变量
extern uint16_t Back_time;    //后退停止时间专用变量

extern uint16_t stop_time_cnt;//巡线停止时间
extern uint16_t back_time_cnt;//后退停止时间

#endif