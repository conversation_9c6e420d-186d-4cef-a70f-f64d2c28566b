#include "Motor.h"

#define SET_FOWARD(X)   DL_GPIO_setPins(DIRC_CTRL_PORT, (X))
#define SET_BACKWARD(X) DL_GPIO_clearPins(DIRC_CTRL_PORT, (X))

/**
 * @brief 开启电机
 * 
 */
void Motor_Start(void)
{
    DL_TimerG_startCounter(MotorFront_INST); //开启前轮
    // DL_TimerG_startCounter(MotorBack_INST); //开启后轮

    //电机都停止运行
    Motor_SetPWM(MOTOR_FONT_LEFT, 0);
    Motor_SetPWM(MOTOR_FONT_RIGHT, 0);
    // Motor_SetPWM(MOTOR_BACK_LEFT, 0);
    // Motor_SetPWM(MOTOR_BACK_RIGHT, 0);

    //四轮都正转
    Motor_SetDirc(MOTOR_FONT_LEFT, DIRC_FOWARD);
    Motor_SetDirc(MOTOR_FONT_RIGHT, DIRC_FOWARD);
    // Motor_SetDirc(MOTOR_BACK_LEFT, DIRC_FOWARD);
    // Motor_SetDirc(MOTOR_BACK_RIGHT, DIRC_FOWARD);
}

/**
 * @brief 设置对应的电机PWM值
 * 
 * @param Motor_Num  电机编号
 * @param PWM_Duty PWM占空比
 * @return 返回设置成功与否
 */
bool Motor_SetPWM(MOTOR_Def_t Motor_Num, uint8_t PWM_Duty)
{
    if (PWM_Duty > 100) return false;

    switch (Motor_Num)
    {
        case MOTOR_FONT_LEFT: //设置左前轮
            DL_TimerG_setCaptureCompareValue(MotorFront_INST, PWM_Duty, DL_TIMER_CC_0_INDEX);
            return true;

        case MOTOR_FONT_RIGHT: //设置右前轮
            DL_TimerG_setCaptureCompareValue(MotorFront_INST, PWM_Duty, DL_TIMER_CC_1_INDEX);
            return true;

        // case MOTOR_BACK_LEFT: //设置左后轮
        //     DL_TimerG_setCaptureCompareValue(MotorBack_INST, PWM_Duty, DL_TIMER_CC_0_INDEX);
        //     return true;

        // case MOTOR_BACK_RIGHT: //设置右后轮
        //     DL_TimerG_setCaptureCompareValue(MotorBack_INST, PWM_Duty, DL_TIMER_CC_1_INDEX);
        //     return true;

        default: //未知轮
            return false;
    }
}

/**
 * @brief 设置电机正反转
 * 
 * @param Motor_Num 电机编号
 * @param DIRC 方向
 * @return 返回设置成功与否
 */
bool Motor_SetDirc(MOTOR_Def_t Motor_Num, MOTOR_Def_t DIRC)
{
    if (DIRC != DIRC_FOWARD && DIRC != DIRC_BACKWARD) return false;

    if (DIRC == DIRC_FOWARD) SET_FOWARD(Motor_Num);
    else SET_BACKWARD(Motor_Num);
    return true;
}