******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 21:52:33 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007099


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008af8  00017508  R  X
  SRAM                  20200000   00008000  000006c5  0000793b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008af8   00008af8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007bb0   00007bb0    r-x .text
  00007c70    00007c70    00000e40   00000e40    r-- .rodata
  00008ab0    00008ab0    00000048   00000048    r-- .cinit
20200000    20200000    000004c7   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000077   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007bb0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001efc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020f0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022dc    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000024b8    000001b0     Task.o (.text.Task_Start)
                  00002668    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002808    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000299a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000299c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002b24    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002c9c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e0c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002f50    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000308c    00000138     PID_Param.o (.text.VelocityRing_PID_Realize)
                  000031c4    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000032f8    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000342c    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  0000355c    00000128     Task_App.o (.text.Task_PID)
                  00003684    00000128     inv_mpu.o (.text.mpu_init)
                  000037ac    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000038d0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000039f0    00000110     OLED.o (.text.OLED_Init)
                  00003b00    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c0c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d14    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003e18    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003f18    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004004    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000040e8    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000041cc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000042a8    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004380    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004458    000000d4     inv_mpu.o (.text.set_int_enable)
                  0000452c    000000d0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000045fc    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000046cc    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004790    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004854    000000c0     PID_Param.o (.text.LocationRing_PID_Realize)
                  00004914    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000049d0    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004a88    000000b4     Task.o (.text.Task_Add)
                  00004b3c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004be8    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004c94    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004d36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004d38    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004dd4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004e6c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004f04    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00004f9a    00000002     --HOLE-- [fill = 0]
                  00004f9c    00000094     MyConfig.o (.text.Car_Tracking)
                  00005030    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000050bc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005148    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000051d4    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005260    00000084     Motor.o (.text.Load_Motor_PWM)
                  000052e4    00000084     Serial.o (.text.MyPrintf)
                  00005368    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000053ec    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005470    00000084     main.o (.text.main)
                  000054f4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005576    00000002     --HOLE-- [fill = 0]
                  00005578    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000055f4    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005668    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005670    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000056e4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005758    00000070     Motor.o (.text.Motor_SetPWM)
                  000057c8    0000006c     MyConfig.o (.text.LocationRing_Out)
                  00005834    0000006c     Task_App.o (.text.Task_Init)
                  000058a0    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000590a    00000002     --HOLE-- [fill = 0]
                  0000590c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005974    00000068     Motor.o (.text.read_encoder)
                  000059dc    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005a42    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005aa8    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005b0c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005b70    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005bd2    00000002     --HOLE-- [fill = 0]
                  00005bd4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005c36    00000002     --HOLE-- [fill = 0]
                  00005c38    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005c98    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005cf8    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005d58    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005db8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005e16    00000002     --HOLE-- [fill = 0]
                  00005e18    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005e74    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005ed0    0000005c     Task_App.o (.text.Task_Encoder)
                  00005f2c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005f88    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005fe4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006040    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006098    00000058     Serial.o (.text.Serial_Init)
                  000060f0    00000058     Task_App.o (.text.Task_IdleFunction)
                  00006148    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000061a0    00000058            : _printfi.c.obj (.text._pconv_f)
                  000061f8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000624e    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000062a0    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000062f0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006340    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006390    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000063dc    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006428    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006472    00000002     --HOLE-- [fill = 0]
                  00006474    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000064bc    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006504    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000654c    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006594    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000065d8    00000044     PID_Param.o (.text.PID_Param_Init)
                  0000661c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006660    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000066a4    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000066e8    00000040     MyConfig.o (.text.VelocityRing_Out)
                  00006728    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006768    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000067a8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000067e8    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006828    0000003e     Task.o (.text.Task_CMP)
                  00006866    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000068a4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000068e0    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000691c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006958    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006994    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000069d0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006a0c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006a48    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006a84    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006abe    00000002     --HOLE-- [fill = 0]
                  00006ac0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006afa    00000002     --HOLE-- [fill = 0]
                  00006afc    00000038     Motor.o (.text.Motor_Start)
                  00006b34    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006b6c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006ba0    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006bd4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006c08    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006c3c    00000034     Task_App.o (.text.Task_Serial)
                  00006c70    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006ca4    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006cd6    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006d08    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006d38    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006d68    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006d98    00000030     Interrupt.o (.text.SysTick_Handler)
                  00006dc8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006df8    00000030            : vsnprintf.c.obj (.text._outs)
                  00006e28    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006e58    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006e88    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006eb4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006ee0    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006f08    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006f30    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006f58    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006f80    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006fa8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006fd0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006ff8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007020    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007048    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007070    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007098    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000070c0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000070e6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000710c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007132    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007158    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000717c    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  000071a0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000071c4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000071e6    00000002     --HOLE-- [fill = 0]
                  000071e8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007208    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007228    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00007248    00000020     SysTick.o (.text.Delay)
                  00007268    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007288    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000072a6    00000002     --HOLE-- [fill = 0]
                  000072a8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000072c6    00000002     --HOLE-- [fill = 0]
                  000072c8    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000072e4    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007300    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000731c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007338    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007354    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007370    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000738c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000073a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000073c4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000073e0    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000073fc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007418    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007434    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007450    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000746c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007488    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000074a4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000074c0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000074dc    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000074f8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007510    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007528    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007540    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007558    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007570    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007588    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000075a0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000075b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000075d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000075e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007600    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007618    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007630    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007648    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007660    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007678    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007690    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000076a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000076c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000076d8    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000076f0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007708    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007720    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007738    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007750    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007768    00000018     OLED.o (.text.DL_I2C_reset)
                  00007780    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007798    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000077b0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000077c8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000077e0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000077f8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007810    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007828    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007840    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007858    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007870    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007888    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000078a0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000078b8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000078d0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000078e8    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007900    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007916    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  0000792c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007942    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007958    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000796e    00000016     SysTick.o (.text.SysGetTick)
                  00007984    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007998    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000079ac    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000079c0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000079d4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000079e8    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000079fc    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007a10    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007a24    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007a38    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007a4c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007a60    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007a74    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007a88    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007a9c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007ab0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007ac2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007ad4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007ae6    00000002     --HOLE-- [fill = 0]
                  00007ae8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007af8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007b08    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007b18    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007b28    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007b38    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007b46    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007b54    0000000e     MPU6050.o (.text.tap_cb)
                  00007b62    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007b70    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007b7c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007b88    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007b94    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007b9e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007ba8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007bb8    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007bc2    00000002     --HOLE-- [fill = 0]
                  00007bc4    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007bd4    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007bde    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007be8    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007bf2    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007bfc    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007c0c    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007c16    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007c1e    00000002     --HOLE-- [fill = 0]
                  00007c20    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007c28    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007c2e    00000002     --HOLE-- [fill = 0]
                  00007c30    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007c40    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007c46    00000006            : exit.c.obj (.text:abort)
                  00007c4c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007c50    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007c54    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007c58    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007c5c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007c6c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00008ab0    00000048     
                  00008ab0    0000001e     (.cinit..data.load) [load image, compression = lzss]
                  00008ace    00000002     --HOLE-- [fill = 0]
                  00008ad0    0000000c     (__TI_handler_table)
                  00008adc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008ae4    00000010     (__TI_cinit_table)
                  00008af4    00000004     --HOLE-- [fill = 0]

.rodata    0    00007c70    00000e40     
                  00007c70    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008866    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008870    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008971    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008978    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000089b8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000089e0    00000028     inv_mpu.o (.rodata.test)
                  00008a08    0000001e     inv_mpu.o (.rodata.reg)
                  00008a26    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008a28    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008a40    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008a58    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008a69    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008a7a    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008a87    00000001     --HOLE-- [fill = 0]
                  00008a88    0000000c     inv_mpu.o (.rodata.hw)
                  00008a94    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008a9c    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008aa4    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008aa8    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008aab    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008aad    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008aaf    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000077     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004b4    00000004     SysTick.o (.data.delayTick)
                  202004b8    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004bc    00000004     SysTick.o (.data.uwTick)
                  202004c0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004c2    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004c3    00000001     Key_Led.o (.data.Task_Flag)
                  202004c4    00000001     Task.o (.data.Task_Num)
                  202004c5    00000001     Key_Led.o (.data.Task_State)
                  202004c6    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3274    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     636     32        6      
       Interrupt.o                    450     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1086    32        12     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Serial.o                       424     0         512    
       Task.o                         674     0         241    
       Task1.o                        744     0         9      
       PID_Param.o                    572     0         72     
       Motor.o                        564     0         4      
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7212    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       66        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31632   3904      1733   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008ae4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008ab0, load size=0000001e bytes, run addr=20200450, run size=00000077 bytes, compression=lzss
	.bss: load addr=00008adc, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008ad0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002809     00007ba8     00007ba6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004005     00007bc4     00007bc0   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007bdc          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007bf0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007c1c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007c44          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b01     00007bfc     00007bfa   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002813     00007c30     00007c2c   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007c56          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007099     00007c5c     00007c58   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007c4d  ADC0_IRQHandler                      
00007c4d  ADC1_IRQHandler                      
00007c4d  AES_IRQHandler                       
00007c50  C$$EXIT                              
00007c4d  CANFD0_IRQHandler                    
00004f9d  Car_Tracking                         
00007c4d  DAC0_IRQHandler                      
00007b95  DL_Common_delayCycles                
00006391  DL_DMA_initChannel                   
00005db9  DL_I2C_fillControllerTXFIFO          
00006959  DL_I2C_flushControllerTXFIFO         
00007133  DL_I2C_setClockConfig                
000041cd  DL_SYSCTL_configSYSPLL               
00005aa9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006595  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003d15  DL_Timer_initFourCCPWMMode           
00007489  DL_Timer_setCaptCompUpdateMethod     
00007829  DL_Timer_setCaptureCompareOutCtl     
00007af9  DL_Timer_setCaptureCompareValue      
000074a5  DL_Timer_setClockConfig              
00006475  DL_UART_init                         
00007ab1  DL_UART_setClockConfig               
00007229  DL_UART_transmitDataBlocking         
00007c4d  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00007c4d  Default_Handler                      
00007249  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00007c4d  GROUP0_IRQHandler                    
0000452d  GROUP1_IRQHandler                    
00007c51  HOSTexit                             
00007c4d  HardFault_Handler                    
00007c4d  I2C0_IRQHandler                      
00007c4d  I2C1_IRQHandler                      
000058a1  I2C_OLED_Clear                       
00004dd5  I2C_OLED_WR_Byte                     
00005c39  I2C_OLED_i2c_sda_unlock              
00006d39  Interrupt_Init                       
00005261  Load_Motor_PWM                       
000057c9  LocationRing_Out                     
00004855  LocationRing_PID_Realize             
0000717d  LocationRing_VelocityRing_Control    
00002e0d  MPU6050_Init                         
00005e19  Motor_SetDirc                        
00005759  Motor_SetPWM                         
00006afd  Motor_Start                          
000052e5  MyPrintf                             
00007c4d  NMI_Handler                          
000039f1  OLED_Init                            
202003ac  PID                                  
000065d9  PID_Param_Init                       
202003f4  Param                                
00007c4d  PendSV_Handler                       
00007c4d  RTC_IRQHandler                       
00001885  Read_Quad                            
00007c59  Reset_Handler                        
00007c4d  SPI0_IRQHandler                      
00007c4d  SPI1_IRQHandler                      
00007c4d  SVC_Handler                          
00006d69  SYSCFG_DL_DMA_CH_RX_init             
000078d1  SYSCFG_DL_DMA_CH_TX_init             
00007b71  SYSCFG_DL_DMA_init                   
000020f1  SYSCFG_DL_GPIO_init                  
00006041  SYSCFG_DL_I2C_MPU6050_init           
00005b0d  SYSCFG_DL_I2C_OLED_init              
00005031  SYSCFG_DL_MotorFront_init            
00005e75  SYSCFG_DL_SYSCTL_init                
00007b09  SYSCFG_DL_SYSTICK_init               
00005369  SYSCFG_DL_UART0_init                 
00006c09  SYSCFG_DL_init                       
000050bd  SYSCFG_DL_initPower                  
00006099  Serial_Init                          
20200000  Serial_RxData                        
0000796f  SysGetTick                           
00006d99  SysTick_Handler                      
00007049  SysTick_Increasment                  
00007b7d  Sys_GetTick                          
00007c4d  TIMA0_IRQHandler                     
00007c4d  TIMA1_IRQHandler                     
00007c4d  TIMG0_IRQHandler                     
00007c4d  TIMG12_IRQHandler                    
00007c4d  TIMG6_IRQHandler                     
00007c4d  TIMG7_IRQHandler                     
00007c4d  TIMG8_IRQHandler                     
00007ac3  TI_memcpy_small                      
00007b63  TI_memset_small                      
000010ed  Task_1                               
00004a89  Task_Add                             
00005ed1  Task_Encoder                         
202004c3  Task_Flag                            
000060f1  Task_IdleFunction                    
00005835  Task_Init                            
0000355d  Task_PID                             
00006c3d  Task_Serial                          
000024b9  Task_Start                           
202004c5  Task_State                           
00007c4d  UART0_IRQHandler                     
00007c4d  UART1_IRQHandler                     
00007c4d  UART2_IRQHandler                     
00007c4d  UART3_IRQHandler                     
000066e9  VelocityRing_Out                     
0000308d  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008ae4  __TI_CINIT_Base                      
00008af4  __TI_CINIT_Limit                     
00008af4  __TI_CINIT_Warm                      
00008ad0  __TI_Handler_Table_Base              
00008adc  __TI_Handler_Table_Limit             
00006a49  __TI_auto_init_nobinit_nopinit       
00005579  __TI_decompress_lzss                 
00007ad5  __TI_decompress_none                 
00006149  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007b29  __TI_zero_init                       
00002813  __adddf3                             
0000438b  __addsf3                             
00008870  __aeabi_ctype_table_                 
00008870  __aeabi_ctype_table_C                
00005671  __aeabi_d2f                          
00006429  __aeabi_d2iz                         
00002813  __aeabi_dadd                         
00005b71  __aeabi_dcmpeq                       
00005bad  __aeabi_dcmpge                       
00005bc1  __aeabi_dcmpgt                       
00005b99  __aeabi_dcmple                       
00005b85  __aeabi_dcmplt                       
00003b01  __aeabi_ddiv                         
00004005  __aeabi_dmul                         
00002809  __aeabi_dsub                         
202004b0  __aeabi_errno                        
00005669  __aeabi_errno_addr                   
00006769  __aeabi_f2d                          
00006b35  __aeabi_f2iz                         
0000438b  __aeabi_fadd                         
00005bd5  __aeabi_fcmpeq                       
00005c11  __aeabi_fcmpge                       
00005c25  __aeabi_fcmpgt                       
00005bfd  __aeabi_fcmple                       
00005be9  __aeabi_fcmplt                       
000054f5  __aeabi_fdiv                         
00005149  __aeabi_fmul                         
00004381  __aeabi_fsub                         
00006eb5  __aeabi_i2d                          
000069d1  __aeabi_i2f                          
000061f9  __aeabi_idiv                         
0000299b  __aeabi_idiv0                        
000061f9  __aeabi_idivmod                      
00004d37  __aeabi_ldiv0                        
000072a9  __aeabi_llsl                         
000071a1  __aeabi_lmul                         
00007b89  __aeabi_memclr                       
00007b89  __aeabi_memclr4                      
00007b89  __aeabi_memclr8                      
00007c21  __aeabi_memcpy                       
00007c21  __aeabi_memcpy4                      
00007c21  __aeabi_memcpy8                      
00007b39  __aeabi_memset                       
00007b39  __aeabi_memset4                      
00007b39  __aeabi_memset8                      
00007071  __aeabi_ui2f                         
00006729  __aeabi_uidiv                        
00006729  __aeabi_uidivmod                     
00007a61  __aeabi_uldivmod                     
000072a9  __ashldi3                            
ffffffff  __binit__                            
0000590d  __cmpdf2                             
00006a85  __cmpsf2                             
00003b01  __divdf3                             
000054f5  __divsf3                             
0000590d  __eqdf2                              
00006a85  __eqsf2                              
00006769  __extendsfdf2                        
00006429  __fixdfsi                            
00006b35  __fixsfsi                            
00006eb5  __floatsidf                          
000069d1  __floatsisf                          
00007071  __floatunsisf                        
000055f5  __gedf2                              
00006a0d  __gesf2                              
000055f5  __gtdf2                              
00006a0d  __gtsf2                              
0000590d  __ledf2                              
00006a85  __lesf2                              
0000590d  __ltdf2                              
00006a85  __ltsf2                              
UNDEFED   __mpu_init                           
00004005  __muldf3                             
000071a1  __muldi3                             
00006ac1  __muldsi3                            
00005149  __mulsf3                             
0000590d  __nedf2                              
00006a85  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002809  __subdf3                             
00004381  __subsf3                             
00005671  __truncdfsf2                         
00004c95  __udivmoddi4                         
00007099  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007c6d  _system_pre_init                     
00007c47  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000299d  atan2                                
0000299d  atan2l                               
00000df5  atanl                                
000067a9  atoi                                 
ffffffff  binit                                
202004b4  delayTick                            
000064bd  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005c99  dmp_enable_gyro_cal                  
00006505  dmp_enable_lp_quat                   
000074dd  dmp_load_motion_driver_firmware      
00001efd  dmp_read_fifo                        
00007a75  dmp_register_android_orient_cb       
00007a89  dmp_register_tap_cb                  
00004e6d  dmp_set_fifo_rate                    
00002b25  dmp_set_orientation                  
0000661d  dmp_set_shake_reject_thresh          
00006ca5  dmp_set_shake_reject_time            
00006cd7  dmp_set_shake_reject_timeout         
00005a43  dmp_set_tap_axes                     
00006661  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00006e29  dmp_set_tap_time                     
00006e59  dmp_set_tap_time_multi               
202004c6  enable_group1_irq                    
00005f2d  frexp                                
00005f2d  frexpl                               
202002f0  gMotorFrontBackup                    
00008a88  hw                                   
00000000  interruptVectors                     
000042a9  ldexp                                
000042a9  ldexpl                               
00005471  main                                 
000071c5  memccpy                              
00007269  memcmp                               
20200427  more                                 
00005cf9  mpu6050_i2c_sda_unlock               
00004915  mpu_configure_fifo                   
000056e5  mpu_get_accel_fsr                    
00005d59  mpu_get_gyro_fsr                     
00006c71  mpu_get_sample_rate                  
00003685  mpu_init                             
000037ad  mpu_load_firmware                    
00003e19  mpu_lp_accel_mode                    
00003c0d  mpu_read_fifo_stream                 
00004b3d  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
000040e9  mpu_set_accel_fsr                    
00002669  mpu_set_bypass                       
000049d1  mpu_set_dmp_state                    
000046cd  mpu_set_gyro_fsr                     
00004d39  mpu_set_int_latched                  
000045fd  mpu_set_lpf                          
00003f19  mpu_set_sample_rate                  
0000342d  mpu_set_sensors                      
00004be9  mpu_write_mem                        
000031c5  mspm0_i2c_read                       
00004791  mspm0_i2c_write                      
000032f9  qsort                                
2020040c  quat                                 
00005975  read_encoder                         
00008a08  reg                                  
000042a9  scalbn                               
000042a9  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002c9d  sqrt                                 
00002c9d  sqrtl                                
2020044a  stop_time_cnt                        
000089e0  test                                 
2020044c  time                                 
202004bc  uwTick                               
000067e9  vsnprintf                            
00007b19  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001efd  dmp_read_fifo                        
000020f1  SYSCFG_DL_GPIO_init                  
000024b9  Task_Start                           
00002669  mpu_set_bypass                       
00002809  __aeabi_dsub                         
00002809  __subdf3                             
00002813  __adddf3                             
00002813  __aeabi_dadd                         
0000299b  __aeabi_idiv0                        
0000299d  atan2                                
0000299d  atan2l                               
00002b25  dmp_set_orientation                  
00002c9d  sqrt                                 
00002c9d  sqrtl                                
00002e0d  MPU6050_Init                         
0000308d  VelocityRing_PID_Realize             
000031c5  mspm0_i2c_read                       
000032f9  qsort                                
0000342d  mpu_set_sensors                      
0000355d  Task_PID                             
00003685  mpu_init                             
000037ad  mpu_load_firmware                    
000039f1  OLED_Init                            
00003b01  __aeabi_ddiv                         
00003b01  __divdf3                             
00003c0d  mpu_read_fifo_stream                 
00003d15  DL_Timer_initFourCCPWMMode           
00003e19  mpu_lp_accel_mode                    
00003f19  mpu_set_sample_rate                  
00004005  __aeabi_dmul                         
00004005  __muldf3                             
000040e9  mpu_set_accel_fsr                    
000041cd  DL_SYSCTL_configSYSPLL               
000042a9  ldexp                                
000042a9  ldexpl                               
000042a9  scalbn                               
000042a9  scalbnl                              
00004381  __aeabi_fsub                         
00004381  __subsf3                             
0000438b  __addsf3                             
0000438b  __aeabi_fadd                         
0000452d  GROUP1_IRQHandler                    
000045fd  mpu_set_lpf                          
000046cd  mpu_set_gyro_fsr                     
00004791  mspm0_i2c_write                      
00004855  LocationRing_PID_Realize             
00004915  mpu_configure_fifo                   
000049d1  mpu_set_dmp_state                    
00004a89  Task_Add                             
00004b3d  mpu_read_mem                         
00004be9  mpu_write_mem                        
00004c95  __udivmoddi4                         
00004d37  __aeabi_ldiv0                        
00004d39  mpu_set_int_latched                  
00004dd5  I2C_OLED_WR_Byte                     
00004e6d  dmp_set_fifo_rate                    
00004f9d  Car_Tracking                         
00005031  SYSCFG_DL_MotorFront_init            
000050bd  SYSCFG_DL_initPower                  
00005149  __aeabi_fmul                         
00005149  __mulsf3                             
00005261  Load_Motor_PWM                       
000052e5  MyPrintf                             
00005369  SYSCFG_DL_UART0_init                 
00005471  main                                 
000054f5  __aeabi_fdiv                         
000054f5  __divsf3                             
00005579  __TI_decompress_lzss                 
000055f5  __gedf2                              
000055f5  __gtdf2                              
00005669  __aeabi_errno_addr                   
00005671  __aeabi_d2f                          
00005671  __truncdfsf2                         
000056e5  mpu_get_accel_fsr                    
00005759  Motor_SetPWM                         
000057c9  LocationRing_Out                     
00005835  Task_Init                            
000058a1  I2C_OLED_Clear                       
0000590d  __cmpdf2                             
0000590d  __eqdf2                              
0000590d  __ledf2                              
0000590d  __ltdf2                              
0000590d  __nedf2                              
00005975  read_encoder                         
00005a43  dmp_set_tap_axes                     
00005aa9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005b0d  SYSCFG_DL_I2C_OLED_init              
00005b71  __aeabi_dcmpeq                       
00005b85  __aeabi_dcmplt                       
00005b99  __aeabi_dcmple                       
00005bad  __aeabi_dcmpge                       
00005bc1  __aeabi_dcmpgt                       
00005bd5  __aeabi_fcmpeq                       
00005be9  __aeabi_fcmplt                       
00005bfd  __aeabi_fcmple                       
00005c11  __aeabi_fcmpge                       
00005c25  __aeabi_fcmpgt                       
00005c39  I2C_OLED_i2c_sda_unlock              
00005c99  dmp_enable_gyro_cal                  
00005cf9  mpu6050_i2c_sda_unlock               
00005d59  mpu_get_gyro_fsr                     
00005db9  DL_I2C_fillControllerTXFIFO          
00005e19  Motor_SetDirc                        
00005e75  SYSCFG_DL_SYSCTL_init                
00005ed1  Task_Encoder                         
00005f2d  frexp                                
00005f2d  frexpl                               
00006041  SYSCFG_DL_I2C_MPU6050_init           
00006099  Serial_Init                          
000060f1  Task_IdleFunction                    
00006149  __TI_ltoa                            
000061f9  __aeabi_idiv                         
000061f9  __aeabi_idivmod                      
00006391  DL_DMA_initChannel                   
00006429  __aeabi_d2iz                         
00006429  __fixdfsi                            
00006475  DL_UART_init                         
000064bd  dmp_enable_6x_lp_quat                
00006505  dmp_enable_lp_quat                   
00006595  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000065d9  PID_Param_Init                       
0000661d  dmp_set_shake_reject_thresh          
00006661  dmp_set_tap_count                    
000066e9  VelocityRing_Out                     
00006729  __aeabi_uidiv                        
00006729  __aeabi_uidivmod                     
00006769  __aeabi_f2d                          
00006769  __extendsfdf2                        
000067a9  atoi                                 
000067e9  vsnprintf                            
00006959  DL_I2C_flushControllerTXFIFO         
000069d1  __aeabi_i2f                          
000069d1  __floatsisf                          
00006a0d  __gesf2                              
00006a0d  __gtsf2                              
00006a49  __TI_auto_init_nobinit_nopinit       
00006a85  __cmpsf2                             
00006a85  __eqsf2                              
00006a85  __lesf2                              
00006a85  __ltsf2                              
00006a85  __nesf2                              
00006ac1  __muldsi3                            
00006afd  Motor_Start                          
00006b35  __aeabi_f2iz                         
00006b35  __fixsfsi                            
00006c09  SYSCFG_DL_init                       
00006c3d  Task_Serial                          
00006c71  mpu_get_sample_rate                  
00006ca5  dmp_set_shake_reject_time            
00006cd7  dmp_set_shake_reject_timeout         
00006d39  Interrupt_Init                       
00006d69  SYSCFG_DL_DMA_CH_RX_init             
00006d99  SysTick_Handler                      
00006e29  dmp_set_tap_time                     
00006e59  dmp_set_tap_time_multi               
00006eb5  __aeabi_i2d                          
00006eb5  __floatsidf                          
00007049  SysTick_Increasment                  
00007071  __aeabi_ui2f                         
00007071  __floatunsisf                        
00007099  _c_int00_noargs                      
00007133  DL_I2C_setClockConfig                
0000717d  LocationRing_VelocityRing_Control    
000071a1  __aeabi_lmul                         
000071a1  __muldi3                             
000071c5  memccpy                              
00007229  DL_UART_transmitDataBlocking         
00007249  Delay                                
00007269  memcmp                               
000072a9  __aeabi_llsl                         
000072a9  __ashldi3                            
00007489  DL_Timer_setCaptCompUpdateMethod     
000074a5  DL_Timer_setClockConfig              
000074dd  dmp_load_motion_driver_firmware      
00007829  DL_Timer_setCaptureCompareOutCtl     
000078d1  SYSCFG_DL_DMA_CH_TX_init             
0000796f  SysGetTick                           
00007a61  __aeabi_uldivmod                     
00007a75  dmp_register_android_orient_cb       
00007a89  dmp_register_tap_cb                  
00007ab1  DL_UART_setClockConfig               
00007ac3  TI_memcpy_small                      
00007ad5  __TI_decompress_none                 
00007af9  DL_Timer_setCaptureCompareValue      
00007b09  SYSCFG_DL_SYSTICK_init               
00007b19  wcslen                               
00007b29  __TI_zero_init                       
00007b39  __aeabi_memset                       
00007b39  __aeabi_memset4                      
00007b39  __aeabi_memset8                      
00007b63  TI_memset_small                      
00007b71  SYSCFG_DL_DMA_init                   
00007b7d  Sys_GetTick                          
00007b89  __aeabi_memclr                       
00007b89  __aeabi_memclr4                      
00007b89  __aeabi_memclr8                      
00007b95  DL_Common_delayCycles                
00007c21  __aeabi_memcpy                       
00007c21  __aeabi_memcpy4                      
00007c21  __aeabi_memcpy8                      
00007c47  abort                                
00007c4d  ADC0_IRQHandler                      
00007c4d  ADC1_IRQHandler                      
00007c4d  AES_IRQHandler                       
00007c4d  CANFD0_IRQHandler                    
00007c4d  DAC0_IRQHandler                      
00007c4d  DMA_IRQHandler                       
00007c4d  Default_Handler                      
00007c4d  GROUP0_IRQHandler                    
00007c4d  HardFault_Handler                    
00007c4d  I2C0_IRQHandler                      
00007c4d  I2C1_IRQHandler                      
00007c4d  NMI_Handler                          
00007c4d  PendSV_Handler                       
00007c4d  RTC_IRQHandler                       
00007c4d  SPI0_IRQHandler                      
00007c4d  SPI1_IRQHandler                      
00007c4d  SVC_Handler                          
00007c4d  TIMA0_IRQHandler                     
00007c4d  TIMA1_IRQHandler                     
00007c4d  TIMG0_IRQHandler                     
00007c4d  TIMG12_IRQHandler                    
00007c4d  TIMG6_IRQHandler                     
00007c4d  TIMG7_IRQHandler                     
00007c4d  TIMG8_IRQHandler                     
00007c4d  UART0_IRQHandler                     
00007c4d  UART1_IRQHandler                     
00007c4d  UART2_IRQHandler                     
00007c4d  UART3_IRQHandler                     
00007c50  C$$EXIT                              
00007c51  HOSTexit                             
00007c59  Reset_Handler                        
00007c6d  _system_pre_init                     
00008870  __aeabi_ctype_table_                 
00008870  __aeabi_ctype_table_C                
000089e0  test                                 
00008a08  reg                                  
00008a88  hw                                   
00008ad0  __TI_Handler_Table_Base              
00008adc  __TI_Handler_Table_Limit             
00008ae4  __TI_CINIT_Base                      
00008af4  __TI_CINIT_Limit                     
00008af4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b0  __aeabi_errno                        
202004b4  delayTick                            
202004bc  uwTick                               
202004c3  Task_Flag                            
202004c5  Task_State                           
202004c6  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[303 symbols]
