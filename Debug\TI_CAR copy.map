******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 22:09:13 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007171


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008c08  000173f8  R  X
  SRAM                  20200000   00008000  000006c6  0000793a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008c08   00008c08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007c90   00007c90    r-x .text
  00007d50    00007d50    00000e70   00000e70    r-- .rodata
  00008bc0    00008bc0    00000048   00000048    r-- .cinit
20200000    20200000    000004c8   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000078   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007c90     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001efc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020f0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022dc    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000024b8    000001b0     Task.o (.text.Task_Start)
                  00002668    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002808    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000299a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000299c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002b24    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002c9c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e0c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002f50    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000308c    00000138     PID_Param.o (.text.VelocityRing_PID_Realize)
                  000031c4    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000032f8    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000342c    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  0000355c    0000012c     Task_App.o (.text.Task_PID)
                  00003688    00000128     inv_mpu.o (.text.mpu_init)
                  000037b0    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000038d4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000039f4    00000110     OLED.o (.text.OLED_Init)
                  00003b04    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c10    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d18    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003e1c    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003f1c    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004008    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000040ec    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000041d0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000042ac    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004384    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000445c    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004530    000000d0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004600    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000046d0    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004794    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004858    000000c0     PID_Param.o (.text.LocationRing_PID_Realize)
                  00004918    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000049d4    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004a8c    000000b4     Task.o (.text.Task_Add)
                  00004b40    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004bec    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004c98    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004d3a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004d3c    0000009c     Task_App.o (.text.Task_Serial)
                  00004dd8    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004e74    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004f0c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004fa4    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000503a    00000002     --HOLE-- [fill = 0]
                  0000503c    00000094     MyConfig.o (.text.Car_Tracking)
                  000050d0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  0000515c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000051e8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005274    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005300    00000084     Motor.o (.text.Load_Motor_PWM)
                  00005384    00000084     Serial.o (.text.MyPrintf)
                  00005408    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  0000548c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005510    00000084     main.o (.text.main)
                  00005594    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005616    00000002     --HOLE-- [fill = 0]
                  00005618    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005694    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005708    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005710    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005784    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000057f8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005868    00000070     Task.o (.text.Task_GetMaxUsed)
                  000058d8    0000006c     MyConfig.o (.text.LocationRing_Out)
                  00005944    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  000059ae    00000002     --HOLE-- [fill = 0]
                  000059b0    00000068     Task_App.o (.text.Task_Init)
                  00005a18    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005a80    00000068     Motor.o (.text.read_encoder)
                  00005ae8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005b4e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005bb4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005c18    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005c7c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005cde    00000002     --HOLE-- [fill = 0]
                  00005ce0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005d42    00000002     --HOLE-- [fill = 0]
                  00005d44    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005da4    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005e04    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005e64    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005ec4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005f22    00000002     --HOLE-- [fill = 0]
                  00005f24    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005f80    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005fdc    0000005c     Task_App.o (.text.Task_Encoder)
                  00006038    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006094    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000060f0    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  0000614c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000061a4    00000058     Serial.o (.text.Serial_Init)
                  000061fc    00000058     Task_App.o (.text.Task_IdleFunction)
                  00006254    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000062ac    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006304    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000635a    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000063ac    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000063fc    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000644c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000649c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000064e8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006534    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000657e    00000002     --HOLE-- [fill = 0]
                  00006580    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000065c8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006610    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006658    00000048     OLED.o (.text.mspm0_i2c_disable)
                  000066a0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000066e4    00000044     PID_Param.o (.text.PID_Param_Init)
                  00006728    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  0000676c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000067b0    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000067f4    00000040     MyConfig.o (.text.VelocityRing_Out)
                  00006834    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006874    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000068b4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000068f4    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006934    0000003e     Task.o (.text.Task_CMP)
                  00006972    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000069b0    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000069ec    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006a28    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006a64    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006aa0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006adc    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006b18    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006b54    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006b90    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006bca    00000002     --HOLE-- [fill = 0]
                  00006bcc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006c06    00000002     --HOLE-- [fill = 0]
                  00006c08    00000038     Motor.o (.text.Motor_Start)
                  00006c40    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006c78    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006cac    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006ce0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006d14    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006d48    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006d7c    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006dae    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006de0    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006e10    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006e40    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006e70    00000030     Interrupt.o (.text.SysTick_Handler)
                  00006ea0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006ed0    00000030            : vsnprintf.c.obj (.text._outs)
                  00006f00    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006f30    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006f60    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006f8c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006fb8    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006fe0    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007008    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007030    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007058    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007080    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000070a8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000070d0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000070f8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007120    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007148    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007170    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007198    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000071be    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000071e4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000720a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007230    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007254    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00007278    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  0000729c    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000072be    00000002     --HOLE-- [fill = 0]
                  000072c0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000072e0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007300    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00007320    00000020     SysTick.o (.text.Delay)
                  00007340    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007360    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000737e    00000002     --HOLE-- [fill = 0]
                  00007380    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000739e    00000002     --HOLE-- [fill = 0]
                  000073a0    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000073bc    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000073d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000073f4    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007410    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  0000742c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007448    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007464    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007480    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000749c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000074b8    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000074d4    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000074f0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  0000750c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007528    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007544    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007560    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000757c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007598    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000075b4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000075d0    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000075e8    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007600    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007618    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007630    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007648    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007660    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007678    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007690    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000076a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000076c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000076d8    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000076f0    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007708    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007720    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007738    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007750    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007768    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007780    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007798    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000077b0    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000077c8    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000077e0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000077f8    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007810    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007828    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007840    00000018     OLED.o (.text.DL_I2C_reset)
                  00007858    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007870    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007888    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000078a0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000078b8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000078d0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000078e8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007900    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007918    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007930    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007948    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007960    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007978    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007990    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000079a8    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000079c0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000079d8    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000079ee    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007a04    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007a1a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007a30    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007a46    00000016     SysTick.o (.text.SysGetTick)
                  00007a5c    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007a70    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007a84    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007a98    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007aac    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007ac0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007ad4    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007ae8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007afc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007b10    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007b24    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007b38    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007b4c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007b60    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007b74    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007b88    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007b9a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007bac    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007bbe    00000002     --HOLE-- [fill = 0]
                  00007bc0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007bd0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007be0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007bf0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007c00    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007c10    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007c1e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007c2c    0000000e     MPU6050.o (.text.tap_cb)
                  00007c3a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007c48    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007c54    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007c60    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007c6c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007c76    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007c80    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007c90    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007c9a    00000002     --HOLE-- [fill = 0]
                  00007c9c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007cac    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007cb6    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007cc0    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007cca    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007cd4    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007ce4    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007cee    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007cf6    00000002     --HOLE-- [fill = 0]
                  00007cf8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007d00    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007d06    00000002     --HOLE-- [fill = 0]
                  00007d08    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007d18    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007d1e    00000006            : exit.c.obj (.text:abort)
                  00007d24    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007d28    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007d2c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007d30    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007d34    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007d44    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007d48    00000008     --HOLE-- [fill = 0]

.cinit     0    00008bc0    00000048     
                  00008bc0    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00008bdf    00000001     --HOLE-- [fill = 0]
                  00008be0    0000000c     (__TI_handler_table)
                  00008bec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008bf4    00000010     (__TI_cinit_table)
                  00008c04    00000004     --HOLE-- [fill = 0]

.rodata    0    00007d50    00000e70     
                  00007d50    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008946    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008950    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008a51    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008a58    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008a98    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008ac0    00000028     inv_mpu.o (.rodata.test)
                  00008ae8    0000001e     inv_mpu.o (.rodata.reg)
                  00008b06    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008b08    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008b20    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008b38    00000017     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00008b4f    00000013     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00008b62    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008b73    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008b84    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008b91    00000001     --HOLE-- [fill = 0]
                  00008b92    0000000c     inv_mpu.o (.rodata.hw)
                  00008b9e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008ba0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008ba8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008bb0    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008bb4    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008bb7    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008bb9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000078     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004b4    00000004     SysTick.o (.data.delayTick)
                  202004b8    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004bc    00000004     SysTick.o (.data.uwTick)
                  202004c0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004c2    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004c3    00000001     Key_Led.o (.data.Task_Flag)
                  202004c4    00000001     Task.o (.data.Task_Num)
                  202004c5    00000001     Task_App.o (.data.Task_Serial.monitor_counter)
                  202004c6    00000001     Key_Led.o (.data.Task_State)
                  202004c7    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3274    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     740     74        7      
       Interrupt.o                    450     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1190    74        13     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Task.o                         786     0         241    
       Serial.o                       424     0         512    
       Task1.o                        744     0         9      
       PID_Param.o                    572     0         72     
       Motor.o                        564     0         4      
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7324    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       67        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31848   3947      1734   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008bf4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008bc0, load size=0000001f bytes, run addr=20200450, run size=00000078 bytes, compression=lzss
	.bss: load addr=00008bec, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008be0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002809     00007c80     00007c7e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004009     00007c9c     00007c98   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007cb4          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007cc8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007cf4          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007d1c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b05     00007cd4     00007cd2   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002813     00007d08     00007d04   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007d2e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007171     00007d34     00007d30   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007d25  ADC0_IRQHandler                      
00007d25  ADC1_IRQHandler                      
00007d25  AES_IRQHandler                       
00007d28  C$$EXIT                              
00007d25  CANFD0_IRQHandler                    
0000503d  Car_Tracking                         
00007d25  DAC0_IRQHandler                      
00007c6d  DL_Common_delayCycles                
0000649d  DL_DMA_initChannel                   
00005ec5  DL_I2C_fillControllerTXFIFO          
00006a65  DL_I2C_flushControllerTXFIFO         
0000720b  DL_I2C_setClockConfig                
000041d1  DL_SYSCTL_configSYSPLL               
00005bb5  DL_SYSCTL_setHFCLKSourceHFXTParams   
000066a1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003d19  DL_Timer_initFourCCPWMMode           
00007561  DL_Timer_setCaptCompUpdateMethod     
00007901  DL_Timer_setCaptureCompareOutCtl     
00007bd1  DL_Timer_setCaptureCompareValue      
0000757d  DL_Timer_setClockConfig              
00006581  DL_UART_init                         
00007b89  DL_UART_setClockConfig               
00007301  DL_UART_transmitDataBlocking         
00007d25  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00007d25  Default_Handler                      
00007321  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00007d25  GROUP0_IRQHandler                    
00004531  GROUP1_IRQHandler                    
00007d29  HOSTexit                             
00007d25  HardFault_Handler                    
00007d25  I2C0_IRQHandler                      
00007d25  I2C1_IRQHandler                      
00005945  I2C_OLED_Clear                       
00004e75  I2C_OLED_WR_Byte                     
00005d45  I2C_OLED_i2c_sda_unlock              
00006e11  Interrupt_Init                       
00005301  Load_Motor_PWM                       
000058d9  LocationRing_Out                     
00004859  LocationRing_PID_Realize             
00007255  LocationRing_VelocityRing_Control    
00002e0d  MPU6050_Init                         
00005f25  Motor_SetDirc                        
000057f9  Motor_SetPWM                         
00006c09  Motor_Start                          
00005385  MyPrintf                             
00007d25  NMI_Handler                          
000039f5  OLED_Init                            
202003ac  PID                                  
000066e5  PID_Param_Init                       
202003f4  Param                                
00007d25  PendSV_Handler                       
00007d25  RTC_IRQHandler                       
00001885  Read_Quad                            
00007d31  Reset_Handler                        
00007d25  SPI0_IRQHandler                      
00007d25  SPI1_IRQHandler                      
00007d25  SVC_Handler                          
00006e41  SYSCFG_DL_DMA_CH_RX_init             
000079a9  SYSCFG_DL_DMA_CH_TX_init             
00007c49  SYSCFG_DL_DMA_init                   
000020f1  SYSCFG_DL_GPIO_init                  
0000614d  SYSCFG_DL_I2C_MPU6050_init           
00005c19  SYSCFG_DL_I2C_OLED_init              
000050d1  SYSCFG_DL_MotorFront_init            
00005f81  SYSCFG_DL_SYSCTL_init                
00007be1  SYSCFG_DL_SYSTICK_init               
00005409  SYSCFG_DL_UART0_init                 
00006d15  SYSCFG_DL_init                       
0000515d  SYSCFG_DL_initPower                  
000061a5  Serial_Init                          
20200000  Serial_RxData                        
00007a47  SysGetTick                           
00006e71  SysTick_Handler                      
00007121  SysTick_Increasment                  
00007c55  Sys_GetTick                          
00007d25  TIMA0_IRQHandler                     
00007d25  TIMA1_IRQHandler                     
00007d25  TIMG0_IRQHandler                     
00007d25  TIMG12_IRQHandler                    
00007d25  TIMG6_IRQHandler                     
00007d25  TIMG7_IRQHandler                     
00007d25  TIMG8_IRQHandler                     
00007b9b  TI_memcpy_small                      
00007c3b  TI_memset_small                      
000010ed  Task_1                               
00004a8d  Task_Add                             
00005fdd  Task_Encoder                         
202004c3  Task_Flag                            
00005869  Task_GetMaxUsed                      
000061fd  Task_IdleFunction                    
000059b1  Task_Init                            
0000355d  Task_PID                             
00004d3d  Task_Serial                          
000024b9  Task_Start                           
202004c6  Task_State                           
00007d25  UART0_IRQHandler                     
00007d25  UART1_IRQHandler                     
00007d25  UART2_IRQHandler                     
00007d25  UART3_IRQHandler                     
000067f5  VelocityRing_Out                     
0000308d  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008bf4  __TI_CINIT_Base                      
00008c04  __TI_CINIT_Limit                     
00008c04  __TI_CINIT_Warm                      
00008be0  __TI_Handler_Table_Base              
00008bec  __TI_Handler_Table_Limit             
00006b55  __TI_auto_init_nobinit_nopinit       
00005619  __TI_decompress_lzss                 
00007bad  __TI_decompress_none                 
00006255  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007c01  __TI_zero_init                       
00002813  __adddf3                             
0000438f  __addsf3                             
00008950  __aeabi_ctype_table_                 
00008950  __aeabi_ctype_table_C                
00005711  __aeabi_d2f                          
00006535  __aeabi_d2iz                         
00002813  __aeabi_dadd                         
00005c7d  __aeabi_dcmpeq                       
00005cb9  __aeabi_dcmpge                       
00005ccd  __aeabi_dcmpgt                       
00005ca5  __aeabi_dcmple                       
00005c91  __aeabi_dcmplt                       
00003b05  __aeabi_ddiv                         
00004009  __aeabi_dmul                         
00002809  __aeabi_dsub                         
202004b0  __aeabi_errno                        
00005709  __aeabi_errno_addr                   
00006875  __aeabi_f2d                          
00006c41  __aeabi_f2iz                         
0000438f  __aeabi_fadd                         
00005ce1  __aeabi_fcmpeq                       
00005d1d  __aeabi_fcmpge                       
00005d31  __aeabi_fcmpgt                       
00005d09  __aeabi_fcmple                       
00005cf5  __aeabi_fcmplt                       
00005595  __aeabi_fdiv                         
000051e9  __aeabi_fmul                         
00004385  __aeabi_fsub                         
00006f8d  __aeabi_i2d                          
00006add  __aeabi_i2f                          
00006305  __aeabi_idiv                         
0000299b  __aeabi_idiv0                        
00006305  __aeabi_idivmod                      
00004d3b  __aeabi_ldiv0                        
00007381  __aeabi_llsl                         
00007279  __aeabi_lmul                         
00007c61  __aeabi_memclr                       
00007c61  __aeabi_memclr4                      
00007c61  __aeabi_memclr8                      
00007cf9  __aeabi_memcpy                       
00007cf9  __aeabi_memcpy4                      
00007cf9  __aeabi_memcpy8                      
00007c11  __aeabi_memset                       
00007c11  __aeabi_memset4                      
00007c11  __aeabi_memset8                      
00007149  __aeabi_ui2f                         
00006835  __aeabi_uidiv                        
00006835  __aeabi_uidivmod                     
00007b39  __aeabi_uldivmod                     
00007381  __ashldi3                            
ffffffff  __binit__                            
00005a19  __cmpdf2                             
00006b91  __cmpsf2                             
00003b05  __divdf3                             
00005595  __divsf3                             
00005a19  __eqdf2                              
00006b91  __eqsf2                              
00006875  __extendsfdf2                        
00006535  __fixdfsi                            
00006c41  __fixsfsi                            
00006f8d  __floatsidf                          
00006add  __floatsisf                          
00007149  __floatunsisf                        
00005695  __gedf2                              
00006b19  __gesf2                              
00005695  __gtdf2                              
00006b19  __gtsf2                              
00005a19  __ledf2                              
00006b91  __lesf2                              
00005a19  __ltdf2                              
00006b91  __ltsf2                              
UNDEFED   __mpu_init                           
00004009  __muldf3                             
00007279  __muldi3                             
00006bcd  __muldsi3                            
000051e9  __mulsf3                             
00005a19  __nedf2                              
00006b91  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002809  __subdf3                             
00004385  __subsf3                             
00005711  __truncdfsf2                         
00004c99  __udivmoddi4                         
00007171  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007d45  _system_pre_init                     
00007d1f  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000299d  atan2                                
0000299d  atan2l                               
00000df5  atanl                                
000068b5  atoi                                 
ffffffff  binit                                
202004b4  delayTick                            
000065c9  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005da5  dmp_enable_gyro_cal                  
00006611  dmp_enable_lp_quat                   
000075b5  dmp_load_motion_driver_firmware      
00001efd  dmp_read_fifo                        
00007b4d  dmp_register_android_orient_cb       
00007b61  dmp_register_tap_cb                  
00004f0d  dmp_set_fifo_rate                    
00002b25  dmp_set_orientation                  
00006729  dmp_set_shake_reject_thresh          
00006d7d  dmp_set_shake_reject_time            
00006daf  dmp_set_shake_reject_timeout         
00005b4f  dmp_set_tap_axes                     
0000676d  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00006f01  dmp_set_tap_time                     
00006f31  dmp_set_tap_time_multi               
202004c7  enable_group1_irq                    
00006039  frexp                                
00006039  frexpl                               
202002f0  gMotorFrontBackup                    
00008b92  hw                                   
00000000  interruptVectors                     
000042ad  ldexp                                
000042ad  ldexpl                               
00005511  main                                 
0000729d  memccpy                              
00007341  memcmp                               
20200427  more                                 
00005e05  mpu6050_i2c_sda_unlock               
00004919  mpu_configure_fifo                   
00005785  mpu_get_accel_fsr                    
00005e65  mpu_get_gyro_fsr                     
00006d49  mpu_get_sample_rate                  
00003689  mpu_init                             
000037b1  mpu_load_firmware                    
00003e1d  mpu_lp_accel_mode                    
00003c11  mpu_read_fifo_stream                 
00004b41  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
000040ed  mpu_set_accel_fsr                    
00002669  mpu_set_bypass                       
000049d5  mpu_set_dmp_state                    
000046d1  mpu_set_gyro_fsr                     
00004dd9  mpu_set_int_latched                  
00004601  mpu_set_lpf                          
00003f1d  mpu_set_sample_rate                  
0000342d  mpu_set_sensors                      
00004bed  mpu_write_mem                        
000031c5  mspm0_i2c_read                       
00004795  mspm0_i2c_write                      
000032f9  qsort                                
2020040c  quat                                 
00005a81  read_encoder                         
00008ae8  reg                                  
000042ad  scalbn                               
000042ad  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002c9d  sqrt                                 
00002c9d  sqrtl                                
2020044a  stop_time_cnt                        
00008ac0  test                                 
2020044c  time                                 
202004bc  uwTick                               
000068f5  vsnprintf                            
00007bf1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001efd  dmp_read_fifo                        
000020f1  SYSCFG_DL_GPIO_init                  
000024b9  Task_Start                           
00002669  mpu_set_bypass                       
00002809  __aeabi_dsub                         
00002809  __subdf3                             
00002813  __adddf3                             
00002813  __aeabi_dadd                         
0000299b  __aeabi_idiv0                        
0000299d  atan2                                
0000299d  atan2l                               
00002b25  dmp_set_orientation                  
00002c9d  sqrt                                 
00002c9d  sqrtl                                
00002e0d  MPU6050_Init                         
0000308d  VelocityRing_PID_Realize             
000031c5  mspm0_i2c_read                       
000032f9  qsort                                
0000342d  mpu_set_sensors                      
0000355d  Task_PID                             
00003689  mpu_init                             
000037b1  mpu_load_firmware                    
000039f5  OLED_Init                            
00003b05  __aeabi_ddiv                         
00003b05  __divdf3                             
00003c11  mpu_read_fifo_stream                 
00003d19  DL_Timer_initFourCCPWMMode           
00003e1d  mpu_lp_accel_mode                    
00003f1d  mpu_set_sample_rate                  
00004009  __aeabi_dmul                         
00004009  __muldf3                             
000040ed  mpu_set_accel_fsr                    
000041d1  DL_SYSCTL_configSYSPLL               
000042ad  ldexp                                
000042ad  ldexpl                               
000042ad  scalbn                               
000042ad  scalbnl                              
00004385  __aeabi_fsub                         
00004385  __subsf3                             
0000438f  __addsf3                             
0000438f  __aeabi_fadd                         
00004531  GROUP1_IRQHandler                    
00004601  mpu_set_lpf                          
000046d1  mpu_set_gyro_fsr                     
00004795  mspm0_i2c_write                      
00004859  LocationRing_PID_Realize             
00004919  mpu_configure_fifo                   
000049d5  mpu_set_dmp_state                    
00004a8d  Task_Add                             
00004b41  mpu_read_mem                         
00004bed  mpu_write_mem                        
00004c99  __udivmoddi4                         
00004d3b  __aeabi_ldiv0                        
00004d3d  Task_Serial                          
00004dd9  mpu_set_int_latched                  
00004e75  I2C_OLED_WR_Byte                     
00004f0d  dmp_set_fifo_rate                    
0000503d  Car_Tracking                         
000050d1  SYSCFG_DL_MotorFront_init            
0000515d  SYSCFG_DL_initPower                  
000051e9  __aeabi_fmul                         
000051e9  __mulsf3                             
00005301  Load_Motor_PWM                       
00005385  MyPrintf                             
00005409  SYSCFG_DL_UART0_init                 
00005511  main                                 
00005595  __aeabi_fdiv                         
00005595  __divsf3                             
00005619  __TI_decompress_lzss                 
00005695  __gedf2                              
00005695  __gtdf2                              
00005709  __aeabi_errno_addr                   
00005711  __aeabi_d2f                          
00005711  __truncdfsf2                         
00005785  mpu_get_accel_fsr                    
000057f9  Motor_SetPWM                         
00005869  Task_GetMaxUsed                      
000058d9  LocationRing_Out                     
00005945  I2C_OLED_Clear                       
000059b1  Task_Init                            
00005a19  __cmpdf2                             
00005a19  __eqdf2                              
00005a19  __ledf2                              
00005a19  __ltdf2                              
00005a19  __nedf2                              
00005a81  read_encoder                         
00005b4f  dmp_set_tap_axes                     
00005bb5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005c19  SYSCFG_DL_I2C_OLED_init              
00005c7d  __aeabi_dcmpeq                       
00005c91  __aeabi_dcmplt                       
00005ca5  __aeabi_dcmple                       
00005cb9  __aeabi_dcmpge                       
00005ccd  __aeabi_dcmpgt                       
00005ce1  __aeabi_fcmpeq                       
00005cf5  __aeabi_fcmplt                       
00005d09  __aeabi_fcmple                       
00005d1d  __aeabi_fcmpge                       
00005d31  __aeabi_fcmpgt                       
00005d45  I2C_OLED_i2c_sda_unlock              
00005da5  dmp_enable_gyro_cal                  
00005e05  mpu6050_i2c_sda_unlock               
00005e65  mpu_get_gyro_fsr                     
00005ec5  DL_I2C_fillControllerTXFIFO          
00005f25  Motor_SetDirc                        
00005f81  SYSCFG_DL_SYSCTL_init                
00005fdd  Task_Encoder                         
00006039  frexp                                
00006039  frexpl                               
0000614d  SYSCFG_DL_I2C_MPU6050_init           
000061a5  Serial_Init                          
000061fd  Task_IdleFunction                    
00006255  __TI_ltoa                            
00006305  __aeabi_idiv                         
00006305  __aeabi_idivmod                      
0000649d  DL_DMA_initChannel                   
00006535  __aeabi_d2iz                         
00006535  __fixdfsi                            
00006581  DL_UART_init                         
000065c9  dmp_enable_6x_lp_quat                
00006611  dmp_enable_lp_quat                   
000066a1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000066e5  PID_Param_Init                       
00006729  dmp_set_shake_reject_thresh          
0000676d  dmp_set_tap_count                    
000067f5  VelocityRing_Out                     
00006835  __aeabi_uidiv                        
00006835  __aeabi_uidivmod                     
00006875  __aeabi_f2d                          
00006875  __extendsfdf2                        
000068b5  atoi                                 
000068f5  vsnprintf                            
00006a65  DL_I2C_flushControllerTXFIFO         
00006add  __aeabi_i2f                          
00006add  __floatsisf                          
00006b19  __gesf2                              
00006b19  __gtsf2                              
00006b55  __TI_auto_init_nobinit_nopinit       
00006b91  __cmpsf2                             
00006b91  __eqsf2                              
00006b91  __lesf2                              
00006b91  __ltsf2                              
00006b91  __nesf2                              
00006bcd  __muldsi3                            
00006c09  Motor_Start                          
00006c41  __aeabi_f2iz                         
00006c41  __fixsfsi                            
00006d15  SYSCFG_DL_init                       
00006d49  mpu_get_sample_rate                  
00006d7d  dmp_set_shake_reject_time            
00006daf  dmp_set_shake_reject_timeout         
00006e11  Interrupt_Init                       
00006e41  SYSCFG_DL_DMA_CH_RX_init             
00006e71  SysTick_Handler                      
00006f01  dmp_set_tap_time                     
00006f31  dmp_set_tap_time_multi               
00006f8d  __aeabi_i2d                          
00006f8d  __floatsidf                          
00007121  SysTick_Increasment                  
00007149  __aeabi_ui2f                         
00007149  __floatunsisf                        
00007171  _c_int00_noargs                      
0000720b  DL_I2C_setClockConfig                
00007255  LocationRing_VelocityRing_Control    
00007279  __aeabi_lmul                         
00007279  __muldi3                             
0000729d  memccpy                              
00007301  DL_UART_transmitDataBlocking         
00007321  Delay                                
00007341  memcmp                               
00007381  __aeabi_llsl                         
00007381  __ashldi3                            
00007561  DL_Timer_setCaptCompUpdateMethod     
0000757d  DL_Timer_setClockConfig              
000075b5  dmp_load_motion_driver_firmware      
00007901  DL_Timer_setCaptureCompareOutCtl     
000079a9  SYSCFG_DL_DMA_CH_TX_init             
00007a47  SysGetTick                           
00007b39  __aeabi_uldivmod                     
00007b4d  dmp_register_android_orient_cb       
00007b61  dmp_register_tap_cb                  
00007b89  DL_UART_setClockConfig               
00007b9b  TI_memcpy_small                      
00007bad  __TI_decompress_none                 
00007bd1  DL_Timer_setCaptureCompareValue      
00007be1  SYSCFG_DL_SYSTICK_init               
00007bf1  wcslen                               
00007c01  __TI_zero_init                       
00007c11  __aeabi_memset                       
00007c11  __aeabi_memset4                      
00007c11  __aeabi_memset8                      
00007c3b  TI_memset_small                      
00007c49  SYSCFG_DL_DMA_init                   
00007c55  Sys_GetTick                          
00007c61  __aeabi_memclr                       
00007c61  __aeabi_memclr4                      
00007c61  __aeabi_memclr8                      
00007c6d  DL_Common_delayCycles                
00007cf9  __aeabi_memcpy                       
00007cf9  __aeabi_memcpy4                      
00007cf9  __aeabi_memcpy8                      
00007d1f  abort                                
00007d25  ADC0_IRQHandler                      
00007d25  ADC1_IRQHandler                      
00007d25  AES_IRQHandler                       
00007d25  CANFD0_IRQHandler                    
00007d25  DAC0_IRQHandler                      
00007d25  DMA_IRQHandler                       
00007d25  Default_Handler                      
00007d25  GROUP0_IRQHandler                    
00007d25  HardFault_Handler                    
00007d25  I2C0_IRQHandler                      
00007d25  I2C1_IRQHandler                      
00007d25  NMI_Handler                          
00007d25  PendSV_Handler                       
00007d25  RTC_IRQHandler                       
00007d25  SPI0_IRQHandler                      
00007d25  SPI1_IRQHandler                      
00007d25  SVC_Handler                          
00007d25  TIMA0_IRQHandler                     
00007d25  TIMA1_IRQHandler                     
00007d25  TIMG0_IRQHandler                     
00007d25  TIMG12_IRQHandler                    
00007d25  TIMG6_IRQHandler                     
00007d25  TIMG7_IRQHandler                     
00007d25  TIMG8_IRQHandler                     
00007d25  UART0_IRQHandler                     
00007d25  UART1_IRQHandler                     
00007d25  UART2_IRQHandler                     
00007d25  UART3_IRQHandler                     
00007d28  C$$EXIT                              
00007d29  HOSTexit                             
00007d31  Reset_Handler                        
00007d45  _system_pre_init                     
00008950  __aeabi_ctype_table_                 
00008950  __aeabi_ctype_table_C                
00008ac0  test                                 
00008ae8  reg                                  
00008b92  hw                                   
00008be0  __TI_Handler_Table_Base              
00008bec  __TI_Handler_Table_Limit             
00008bf4  __TI_CINIT_Base                      
00008c04  __TI_CINIT_Limit                     
00008c04  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b0  __aeabi_errno                        
202004b4  delayTick                            
202004bc  uwTick                               
202004c3  Task_Flag                            
202004c6  Task_State                           
202004c7  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[304 symbols]
