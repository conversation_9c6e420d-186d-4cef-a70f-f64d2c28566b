******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 22:21:16 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007309


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008da8  00017258  R  X
  SRAM                  20200000   00008000  000006d2  0000792e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008da8   00008da8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007e30   00007e30    r-x .text
  00007ef0    00007ef0    00000e70   00000e70    r-- .rodata
  00008d60    00008d60    00000048   00000048    r-- .cinit
20200000    20200000    000004d4   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000084   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007e30     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000224     Task_App.o (.text.Task_PID)
                  00001f00    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00002120    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002314    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002500    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000026dc    000001b0     Task.o (.text.Task_Start)
                  0000288c    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002a2c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002bbe    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002bc0    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002d48    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002ec0    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00003030    00000144     MPU6050.o (.text.MPU6050_Init)
                  00003174    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000032b0    00000138     PID_Param.o (.text.VelocityRing_PID_Realize)
                  000033e8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000351c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003650    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003780    00000128     inv_mpu.o (.text.mpu_init)
                  000038a8    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000039cc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003aec    00000114     Interrupt.o (.text.GROUP1_IRQHandler)
                  00003c00    00000110     OLED.o (.text.OLED_Init)
                  00003d10    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003e1c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003f24    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004028    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004128    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004214    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000042f8    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000043dc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000044b8    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004590    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004668    000000d4     inv_mpu.o (.text.set_int_enable)
                  0000473c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  0000480c    000000cc     Motor.o (.text.read_encoder)
                  000048d8    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  0000499c    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004a60    000000c0     PID_Param.o (.text.LocationRing_PID_Realize)
                  00004b20    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004bdc    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004c94    000000b4     Task.o (.text.Task_Add)
                  00004d48    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004df4    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004ea0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004f42    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004f44    0000009c     Task_App.o (.text.Task_Serial)
                  00004fe0    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  0000507c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005114    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000051ac    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005242    00000002     --HOLE-- [fill = 0]
                  00005244    00000094     MyConfig.o (.text.Car_Tracking)
                  000052d8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005364    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000053f0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000547c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005508    00000084     Motor.o (.text.Load_Motor_PWM)
                  0000558c    00000084     Serial.o (.text.MyPrintf)
                  00005610    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005694    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005718    00000084     main.o (.text.main)
                  0000579c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000581e    00000002     --HOLE-- [fill = 0]
                  00005820    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000589c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005910    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005984    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000059f8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005a68    00000070     Task.o (.text.Task_GetMaxUsed)
                  00005ad8    0000006c     MyConfig.o (.text.LocationRing_Out)
                  00005b44    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005bae    00000002     --HOLE-- [fill = 0]
                  00005bb0    00000068     Task_App.o (.text.Task_Init)
                  00005c18    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005c80    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005ce6    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005d4c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005db0    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005e14    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005e76    00000002     --HOLE-- [fill = 0]
                  00005e78    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005eda    00000002     --HOLE-- [fill = 0]
                  00005edc    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005f3c    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005f9c    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005ffc    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  0000605c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000060ba    00000002     --HOLE-- [fill = 0]
                  000060bc    0000005c     Motor.o (.text.Motor_SetDirc)
                  00006118    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006174    0000005c     Task_App.o (.text.Task_Encoder)
                  000061d0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000622c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006288    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000062e4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000633c    00000058     Serial.o (.text.Serial_Init)
                  00006394    00000058     Task_App.o (.text.Task_IdleFunction)
                  000063ec    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006444    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000649c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000064f2    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006544    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006594    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000065e4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006634    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006680    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000066cc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006716    00000002     --HOLE-- [fill = 0]
                  00006718    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006760    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000067a8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  000067f0    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006838    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000687c    00000044     PID_Param.o (.text.PID_Param_Init)
                  000068c0    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006904    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006948    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  0000698c    00000040     MyConfig.o (.text.VelocityRing_Out)
                  000069cc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006a0c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006a4c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006a8c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006acc    0000003e     Task.o (.text.Task_CMP)
                  00006b0a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006b48    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b84    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bc0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bfc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006c38    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006c74    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006cb0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006cec    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006d28    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006d62    00000002     --HOLE-- [fill = 0]
                  00006d64    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006d9e    00000002     --HOLE-- [fill = 0]
                  00006da0    00000038     Motor.o (.text.Motor_Start)
                  00006dd8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006e10    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e44    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e78    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006eac    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006ee0    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006f14    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006f46    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006f78    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006fa8    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006fd8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007008    00000030     Interrupt.o (.text.SysTick_Handler)
                  00007038    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007068    00000030            : vsnprintf.c.obj (.text._outs)
                  00007098    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000070c8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000070f8    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007124    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007150    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007178    00000028     OLED.o (.text.DL_Common_updateReg)
                  000071a0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000071c8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000071f0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007218    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007240    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007268    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007290    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000072b8    00000028     SysTick.o (.text.SysTick_Increasment)
                  000072e0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007308    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007330    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007356    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000737c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000073a2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000073c8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000073ec    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00007410    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007434    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007456    00000002     --HOLE-- [fill = 0]
                  00007458    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007478    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007498    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  000074b8    00000020     SysTick.o (.text.Delay)
                  000074d8    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000074f8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007516    00000002     --HOLE-- [fill = 0]
                  00007518    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007536    00000002     --HOLE-- [fill = 0]
                  00007538    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007554    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007570    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000758c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000075a8    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000075c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000075e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000075fc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007618    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00007634    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007650    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000766c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007688    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000076a4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000076c0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000076dc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000076f8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007714    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007730    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000774c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007768    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007780    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007798    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000077b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000077c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000077e0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000077f8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007810    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007828    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007840    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007858    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007870    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007888    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000078a0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000078b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000078d0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000078e8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007900    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007918    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007930    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007948    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007960    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007978    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007990    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000079a8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000079c0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000079d8    00000018     OLED.o (.text.DL_I2C_reset)
                  000079f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007a08    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007a20    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007a38    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007a50    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007a68    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007a80    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007a98    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007ab0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007ac8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007ae0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007af8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007b10    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007b28    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007b40    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007b58    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007b70    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007b86    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007b9c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007bb2    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007bc8    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007bde    00000016     SysTick.o (.text.SysGetTick)
                  00007bf4    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007c08    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007c1c    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007c30    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007c44    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007c58    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007c6c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007c80    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007c94    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007ca8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007cbc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007cd0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007ce4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007cf8    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007d0c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007d20    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007d32    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007d44    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007d56    00000002     --HOLE-- [fill = 0]
                  00007d58    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007d68    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007d78    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007d88    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007d98    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007da8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007db6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007dc4    0000000e     MPU6050.o (.text.tap_cb)
                  00007dd2    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007de0    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007dec    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007df8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007e04    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007e0e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007e18    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007e28    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007e32    00000002     --HOLE-- [fill = 0]
                  00007e34    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007e44    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007e4e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007e58    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007e62    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007e6c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007e7c    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007e86    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007e8e    00000002     --HOLE-- [fill = 0]
                  00007e90    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007e98    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007ea0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007ea6    00000002     --HOLE-- [fill = 0]
                  00007ea8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007eb8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007ebe    00000006            : exit.c.obj (.text:abort)
                  00007ec4    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007ec8    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007ecc    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007ed0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007ed4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007ee4    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007ee8    00000008     --HOLE-- [fill = 0]

.cinit     0    00008d60    00000048     
                  00008d60    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00008d7f    00000001     --HOLE-- [fill = 0]
                  00008d80    0000000c     (__TI_handler_table)
                  00008d8c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008d94    00000010     (__TI_cinit_table)
                  00008da4    00000004     --HOLE-- [fill = 0]

.rodata    0    00007ef0    00000e70     
                  00007ef0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008ae6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008af0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008bf1    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008bf8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008c38    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008c60    00000028     inv_mpu.o (.rodata.test)
                  00008c88    0000001e     inv_mpu.o (.rodata.reg)
                  00008ca6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008ca8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008cc0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008cd8    00000017     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00008cef    00000013     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00008d02    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008d13    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008d24    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008d31    00000001     --HOLE-- [fill = 0]
                  00008d32    0000000c     inv_mpu.o (.rodata.hw)
                  00008d3e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008d40    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008d48    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008d50    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008d54    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008d57    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008d59    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000084     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     Interrupt.o (.data.GROUP1_IRQHandler.left_last_time)
                  202004b4    00000004     Interrupt.o (.data.GROUP1_IRQHandler.right_last_time)
                  202004b8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004bc    00000004     SysTick.o (.data.delayTick)
                  202004c0    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004c4    00000004     SysTick.o (.data.uwTick)
                  202004c8    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ca    00000002     Task_App.o (.data.Task_PID.last_motor1_pwm)
                  202004cc    00000002     Task_App.o (.data.Task_PID.last_motor2_pwm)
                  202004ce    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004cf    00000001     Key_Led.o (.data.Task_Flag)
                  202004d0    00000001     Task.o (.data.Task_Num)
                  202004d1    00000001     Task_App.o (.data.Task_Serial.monitor_counter)
                  202004d2    00000001     Key_Led.o (.data.Task_State)
                  202004d3    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3274    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     988     74        11     
       Interrupt.o                    518     0         14     
    +--+------------------------------+-------+---------+---------+
       Total:                         1506    74        25     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Task.o                         786     0         241    
       Serial.o                       424     0         512    
       Task1.o                        744     0         9      
       Motor.o                        664     0         4      
       PID_Param.o                    572     0         72     
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7424    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       67        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32264   3947      1746   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008d94 records: 2, size/record: 8, table size: 16
	.data: load addr=00008d60, load size=0000001f bytes, run addr=20200450, run size=00000084 bytes, compression=lzss
	.bss: load addr=00008d8c, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008d80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002a2d     00007e18     00007e16   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004215     00007e34     00007e30   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007e4c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007e60          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007e8c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007ebc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003d11     00007e6c     00007e6a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002a37     00007ea8     00007ea4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007ece          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007309     00007ed4     00007ed0   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007ec5  ADC0_IRQHandler                      
00007ec5  ADC1_IRQHandler                      
00007ec5  AES_IRQHandler                       
00007ec8  C$$EXIT                              
00007ec5  CANFD0_IRQHandler                    
00005245  Car_Tracking                         
00007ec5  DAC0_IRQHandler                      
00007e05  DL_Common_delayCycles                
00006635  DL_DMA_initChannel                   
0000605d  DL_I2C_fillControllerTXFIFO          
00006bfd  DL_I2C_flushControllerTXFIFO         
000073a3  DL_I2C_setClockConfig                
000043dd  DL_SYSCTL_configSYSPLL               
00005d4d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006839  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003f25  DL_Timer_initFourCCPWMMode           
000076f9  DL_Timer_setCaptCompUpdateMethod     
00007a99  DL_Timer_setCaptureCompareOutCtl     
00007d69  DL_Timer_setCaptureCompareValue      
00007715  DL_Timer_setClockConfig              
00006719  DL_UART_init                         
00007d21  DL_UART_setClockConfig               
00007499  DL_UART_transmitDataBlocking         
00007ec5  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00007ec5  Default_Handler                      
000074b9  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00007ec5  GROUP0_IRQHandler                    
00003aed  GROUP1_IRQHandler                    
00007ec9  HOSTexit                             
00007ec5  HardFault_Handler                    
00007ec5  I2C0_IRQHandler                      
00007ec5  I2C1_IRQHandler                      
00005b45  I2C_OLED_Clear                       
0000507d  I2C_OLED_WR_Byte                     
00005edd  I2C_OLED_i2c_sda_unlock              
00006fa9  Interrupt_Init                       
00005509  Load_Motor_PWM                       
00005ad9  LocationRing_Out                     
00004a61  LocationRing_PID_Realize             
000073ed  LocationRing_VelocityRing_Control    
00003031  MPU6050_Init                         
000060bd  Motor_SetDirc                        
000059f9  Motor_SetPWM                         
00006da1  Motor_Start                          
0000558d  MyPrintf                             
00007ec5  NMI_Handler                          
00003c01  OLED_Init                            
202003ac  PID                                  
0000687d  PID_Param_Init                       
202003f4  Param                                
00007ec5  PendSV_Handler                       
00007ec5  RTC_IRQHandler                       
00001885  Read_Quad                            
00007ed1  Reset_Handler                        
00007ec5  SPI0_IRQHandler                      
00007ec5  SPI1_IRQHandler                      
00007ec5  SVC_Handler                          
00006fd9  SYSCFG_DL_DMA_CH_RX_init             
00007b41  SYSCFG_DL_DMA_CH_TX_init             
00007de1  SYSCFG_DL_DMA_init                   
00002315  SYSCFG_DL_GPIO_init                  
000062e5  SYSCFG_DL_I2C_MPU6050_init           
00005db1  SYSCFG_DL_I2C_OLED_init              
000052d9  SYSCFG_DL_MotorFront_init            
00006119  SYSCFG_DL_SYSCTL_init                
00007d79  SYSCFG_DL_SYSTICK_init               
00005611  SYSCFG_DL_UART0_init                 
00006ead  SYSCFG_DL_init                       
00005365  SYSCFG_DL_initPower                  
0000633d  Serial_Init                          
20200000  Serial_RxData                        
00007bdf  SysGetTick                           
00007009  SysTick_Handler                      
000072b9  SysTick_Increasment                  
00007ded  Sys_GetTick                          
00007ec5  TIMA0_IRQHandler                     
00007ec5  TIMA1_IRQHandler                     
00007ec5  TIMG0_IRQHandler                     
00007ec5  TIMG12_IRQHandler                    
00007ec5  TIMG6_IRQHandler                     
00007ec5  TIMG7_IRQHandler                     
00007ec5  TIMG8_IRQHandler                     
00007d33  TI_memcpy_small                      
00007dd3  TI_memset_small                      
000010ed  Task_1                               
00004c95  Task_Add                             
00006175  Task_Encoder                         
202004cf  Task_Flag                            
00005a69  Task_GetMaxUsed                      
00006395  Task_IdleFunction                    
00005bb1  Task_Init                            
00001cdd  Task_PID                             
00004f45  Task_Serial                          
000026dd  Task_Start                           
202004d2  Task_State                           
00007ec5  UART0_IRQHandler                     
00007ec5  UART1_IRQHandler                     
00007ec5  UART2_IRQHandler                     
00007ec5  UART3_IRQHandler                     
0000698d  VelocityRing_Out                     
000032b1  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008d94  __TI_CINIT_Base                      
00008da4  __TI_CINIT_Limit                     
00008da4  __TI_CINIT_Warm                      
00008d80  __TI_Handler_Table_Base              
00008d8c  __TI_Handler_Table_Limit             
00006ced  __TI_auto_init_nobinit_nopinit       
00005821  __TI_decompress_lzss                 
00007d45  __TI_decompress_none                 
000063ed  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007d99  __TI_zero_init                       
00002a37  __adddf3                             
0000459b  __addsf3                             
00008af0  __aeabi_ctype_table_                 
00008af0  __aeabi_ctype_table_C                
00005911  __aeabi_d2f                          
000066cd  __aeabi_d2iz                         
00002a37  __aeabi_dadd                         
00005e15  __aeabi_dcmpeq                       
00005e51  __aeabi_dcmpge                       
00005e65  __aeabi_dcmpgt                       
00005e3d  __aeabi_dcmple                       
00005e29  __aeabi_dcmplt                       
00003d11  __aeabi_ddiv                         
00004215  __aeabi_dmul                         
00002a2d  __aeabi_dsub                         
202004b8  __aeabi_errno                        
00007e91  __aeabi_errno_addr                   
00006a0d  __aeabi_f2d                          
00006dd9  __aeabi_f2iz                         
0000459b  __aeabi_fadd                         
00005e79  __aeabi_fcmpeq                       
00005eb5  __aeabi_fcmpge                       
00005ec9  __aeabi_fcmpgt                       
00005ea1  __aeabi_fcmple                       
00005e8d  __aeabi_fcmplt                       
0000579d  __aeabi_fdiv                         
000053f1  __aeabi_fmul                         
00004591  __aeabi_fsub                         
00007125  __aeabi_i2d                          
00006c75  __aeabi_i2f                          
0000649d  __aeabi_idiv                         
00002bbf  __aeabi_idiv0                        
0000649d  __aeabi_idivmod                      
00004f43  __aeabi_ldiv0                        
00007519  __aeabi_llsl                         
00007411  __aeabi_lmul                         
00007df9  __aeabi_memclr                       
00007df9  __aeabi_memclr4                      
00007df9  __aeabi_memclr8                      
00007e99  __aeabi_memcpy                       
00007e99  __aeabi_memcpy4                      
00007e99  __aeabi_memcpy8                      
00007da9  __aeabi_memset                       
00007da9  __aeabi_memset4                      
00007da9  __aeabi_memset8                      
000072e1  __aeabi_ui2f                         
000069cd  __aeabi_uidiv                        
000069cd  __aeabi_uidivmod                     
00007cd1  __aeabi_uldivmod                     
00007519  __ashldi3                            
ffffffff  __binit__                            
00005c19  __cmpdf2                             
00006d29  __cmpsf2                             
00003d11  __divdf3                             
0000579d  __divsf3                             
00005c19  __eqdf2                              
00006d29  __eqsf2                              
00006a0d  __extendsfdf2                        
000066cd  __fixdfsi                            
00006dd9  __fixsfsi                            
00007125  __floatsidf                          
00006c75  __floatsisf                          
000072e1  __floatunsisf                        
0000589d  __gedf2                              
00006cb1  __gesf2                              
0000589d  __gtdf2                              
00006cb1  __gtsf2                              
00005c19  __ledf2                              
00006d29  __lesf2                              
00005c19  __ltdf2                              
00006d29  __ltsf2                              
UNDEFED   __mpu_init                           
00004215  __muldf3                             
00007411  __muldi3                             
00006d65  __muldsi3                            
000053f1  __mulsf3                             
00005c19  __nedf2                              
00006d29  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002a2d  __subdf3                             
00004591  __subsf3                             
00005911  __truncdfsf2                         
00004ea1  __udivmoddi4                         
00007309  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007ee5  _system_pre_init                     
00007ebf  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002bc1  atan2                                
00002bc1  atan2l                               
00000df5  atanl                                
00006a4d  atoi                                 
ffffffff  binit                                
202004bc  delayTick                            
00006761  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005f3d  dmp_enable_gyro_cal                  
000067a9  dmp_enable_lp_quat                   
0000774d  dmp_load_motion_driver_firmware      
00002121  dmp_read_fifo                        
00007ce5  dmp_register_android_orient_cb       
00007cf9  dmp_register_tap_cb                  
00005115  dmp_set_fifo_rate                    
00002d49  dmp_set_orientation                  
000068c1  dmp_set_shake_reject_thresh          
00006f15  dmp_set_shake_reject_time            
00006f47  dmp_set_shake_reject_timeout         
00005ce7  dmp_set_tap_axes                     
00006905  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00007099  dmp_set_tap_time                     
000070c9  dmp_set_tap_time_multi               
202004d3  enable_group1_irq                    
000061d1  frexp                                
000061d1  frexpl                               
202002f0  gMotorFrontBackup                    
00008d32  hw                                   
00000000  interruptVectors                     
000044b9  ldexp                                
000044b9  ldexpl                               
00005719  main                                 
00007435  memccpy                              
000074d9  memcmp                               
20200427  more                                 
00005f9d  mpu6050_i2c_sda_unlock               
00004b21  mpu_configure_fifo                   
00005985  mpu_get_accel_fsr                    
00005ffd  mpu_get_gyro_fsr                     
00006ee1  mpu_get_sample_rate                  
00003781  mpu_init                             
000038a9  mpu_load_firmware                    
00004029  mpu_lp_accel_mode                    
00003e1d  mpu_read_fifo_stream                 
00004d49  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
000042f9  mpu_set_accel_fsr                    
0000288d  mpu_set_bypass                       
00004bdd  mpu_set_dmp_state                    
000048d9  mpu_set_gyro_fsr                     
00004fe1  mpu_set_int_latched                  
0000473d  mpu_set_lpf                          
00004129  mpu_set_sample_rate                  
00003651  mpu_set_sensors                      
00004df5  mpu_write_mem                        
000033e9  mspm0_i2c_read                       
0000499d  mspm0_i2c_write                      
0000351d  qsort                                
2020040c  quat                                 
0000480d  read_encoder                         
00008c88  reg                                  
000044b9  scalbn                               
000044b9  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002ec1  sqrt                                 
00002ec1  sqrtl                                
2020044a  stop_time_cnt                        
00008c60  test                                 
2020044c  time                                 
202004c4  uwTick                               
00006a8d  vsnprintf                            
00007d89  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001cdd  Task_PID                             
00002121  dmp_read_fifo                        
00002315  SYSCFG_DL_GPIO_init                  
000026dd  Task_Start                           
0000288d  mpu_set_bypass                       
00002a2d  __aeabi_dsub                         
00002a2d  __subdf3                             
00002a37  __adddf3                             
00002a37  __aeabi_dadd                         
00002bbf  __aeabi_idiv0                        
00002bc1  atan2                                
00002bc1  atan2l                               
00002d49  dmp_set_orientation                  
00002ec1  sqrt                                 
00002ec1  sqrtl                                
00003031  MPU6050_Init                         
000032b1  VelocityRing_PID_Realize             
000033e9  mspm0_i2c_read                       
0000351d  qsort                                
00003651  mpu_set_sensors                      
00003781  mpu_init                             
000038a9  mpu_load_firmware                    
00003aed  GROUP1_IRQHandler                    
00003c01  OLED_Init                            
00003d11  __aeabi_ddiv                         
00003d11  __divdf3                             
00003e1d  mpu_read_fifo_stream                 
00003f25  DL_Timer_initFourCCPWMMode           
00004029  mpu_lp_accel_mode                    
00004129  mpu_set_sample_rate                  
00004215  __aeabi_dmul                         
00004215  __muldf3                             
000042f9  mpu_set_accel_fsr                    
000043dd  DL_SYSCTL_configSYSPLL               
000044b9  ldexp                                
000044b9  ldexpl                               
000044b9  scalbn                               
000044b9  scalbnl                              
00004591  __aeabi_fsub                         
00004591  __subsf3                             
0000459b  __addsf3                             
0000459b  __aeabi_fadd                         
0000473d  mpu_set_lpf                          
0000480d  read_encoder                         
000048d9  mpu_set_gyro_fsr                     
0000499d  mspm0_i2c_write                      
00004a61  LocationRing_PID_Realize             
00004b21  mpu_configure_fifo                   
00004bdd  mpu_set_dmp_state                    
00004c95  Task_Add                             
00004d49  mpu_read_mem                         
00004df5  mpu_write_mem                        
00004ea1  __udivmoddi4                         
00004f43  __aeabi_ldiv0                        
00004f45  Task_Serial                          
00004fe1  mpu_set_int_latched                  
0000507d  I2C_OLED_WR_Byte                     
00005115  dmp_set_fifo_rate                    
00005245  Car_Tracking                         
000052d9  SYSCFG_DL_MotorFront_init            
00005365  SYSCFG_DL_initPower                  
000053f1  __aeabi_fmul                         
000053f1  __mulsf3                             
00005509  Load_Motor_PWM                       
0000558d  MyPrintf                             
00005611  SYSCFG_DL_UART0_init                 
00005719  main                                 
0000579d  __aeabi_fdiv                         
0000579d  __divsf3                             
00005821  __TI_decompress_lzss                 
0000589d  __gedf2                              
0000589d  __gtdf2                              
00005911  __aeabi_d2f                          
00005911  __truncdfsf2                         
00005985  mpu_get_accel_fsr                    
000059f9  Motor_SetPWM                         
00005a69  Task_GetMaxUsed                      
00005ad9  LocationRing_Out                     
00005b45  I2C_OLED_Clear                       
00005bb1  Task_Init                            
00005c19  __cmpdf2                             
00005c19  __eqdf2                              
00005c19  __ledf2                              
00005c19  __ltdf2                              
00005c19  __nedf2                              
00005ce7  dmp_set_tap_axes                     
00005d4d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005db1  SYSCFG_DL_I2C_OLED_init              
00005e15  __aeabi_dcmpeq                       
00005e29  __aeabi_dcmplt                       
00005e3d  __aeabi_dcmple                       
00005e51  __aeabi_dcmpge                       
00005e65  __aeabi_dcmpgt                       
00005e79  __aeabi_fcmpeq                       
00005e8d  __aeabi_fcmplt                       
00005ea1  __aeabi_fcmple                       
00005eb5  __aeabi_fcmpge                       
00005ec9  __aeabi_fcmpgt                       
00005edd  I2C_OLED_i2c_sda_unlock              
00005f3d  dmp_enable_gyro_cal                  
00005f9d  mpu6050_i2c_sda_unlock               
00005ffd  mpu_get_gyro_fsr                     
0000605d  DL_I2C_fillControllerTXFIFO          
000060bd  Motor_SetDirc                        
00006119  SYSCFG_DL_SYSCTL_init                
00006175  Task_Encoder                         
000061d1  frexp                                
000061d1  frexpl                               
000062e5  SYSCFG_DL_I2C_MPU6050_init           
0000633d  Serial_Init                          
00006395  Task_IdleFunction                    
000063ed  __TI_ltoa                            
0000649d  __aeabi_idiv                         
0000649d  __aeabi_idivmod                      
00006635  DL_DMA_initChannel                   
000066cd  __aeabi_d2iz                         
000066cd  __fixdfsi                            
00006719  DL_UART_init                         
00006761  dmp_enable_6x_lp_quat                
000067a9  dmp_enable_lp_quat                   
00006839  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000687d  PID_Param_Init                       
000068c1  dmp_set_shake_reject_thresh          
00006905  dmp_set_tap_count                    
0000698d  VelocityRing_Out                     
000069cd  __aeabi_uidiv                        
000069cd  __aeabi_uidivmod                     
00006a0d  __aeabi_f2d                          
00006a0d  __extendsfdf2                        
00006a4d  atoi                                 
00006a8d  vsnprintf                            
00006bfd  DL_I2C_flushControllerTXFIFO         
00006c75  __aeabi_i2f                          
00006c75  __floatsisf                          
00006cb1  __gesf2                              
00006cb1  __gtsf2                              
00006ced  __TI_auto_init_nobinit_nopinit       
00006d29  __cmpsf2                             
00006d29  __eqsf2                              
00006d29  __lesf2                              
00006d29  __ltsf2                              
00006d29  __nesf2                              
00006d65  __muldsi3                            
00006da1  Motor_Start                          
00006dd9  __aeabi_f2iz                         
00006dd9  __fixsfsi                            
00006ead  SYSCFG_DL_init                       
00006ee1  mpu_get_sample_rate                  
00006f15  dmp_set_shake_reject_time            
00006f47  dmp_set_shake_reject_timeout         
00006fa9  Interrupt_Init                       
00006fd9  SYSCFG_DL_DMA_CH_RX_init             
00007009  SysTick_Handler                      
00007099  dmp_set_tap_time                     
000070c9  dmp_set_tap_time_multi               
00007125  __aeabi_i2d                          
00007125  __floatsidf                          
000072b9  SysTick_Increasment                  
000072e1  __aeabi_ui2f                         
000072e1  __floatunsisf                        
00007309  _c_int00_noargs                      
000073a3  DL_I2C_setClockConfig                
000073ed  LocationRing_VelocityRing_Control    
00007411  __aeabi_lmul                         
00007411  __muldi3                             
00007435  memccpy                              
00007499  DL_UART_transmitDataBlocking         
000074b9  Delay                                
000074d9  memcmp                               
00007519  __aeabi_llsl                         
00007519  __ashldi3                            
000076f9  DL_Timer_setCaptCompUpdateMethod     
00007715  DL_Timer_setClockConfig              
0000774d  dmp_load_motion_driver_firmware      
00007a99  DL_Timer_setCaptureCompareOutCtl     
00007b41  SYSCFG_DL_DMA_CH_TX_init             
00007bdf  SysGetTick                           
00007cd1  __aeabi_uldivmod                     
00007ce5  dmp_register_android_orient_cb       
00007cf9  dmp_register_tap_cb                  
00007d21  DL_UART_setClockConfig               
00007d33  TI_memcpy_small                      
00007d45  __TI_decompress_none                 
00007d69  DL_Timer_setCaptureCompareValue      
00007d79  SYSCFG_DL_SYSTICK_init               
00007d89  wcslen                               
00007d99  __TI_zero_init                       
00007da9  __aeabi_memset                       
00007da9  __aeabi_memset4                      
00007da9  __aeabi_memset8                      
00007dd3  TI_memset_small                      
00007de1  SYSCFG_DL_DMA_init                   
00007ded  Sys_GetTick                          
00007df9  __aeabi_memclr                       
00007df9  __aeabi_memclr4                      
00007df9  __aeabi_memclr8                      
00007e05  DL_Common_delayCycles                
00007e91  __aeabi_errno_addr                   
00007e99  __aeabi_memcpy                       
00007e99  __aeabi_memcpy4                      
00007e99  __aeabi_memcpy8                      
00007ebf  abort                                
00007ec5  ADC0_IRQHandler                      
00007ec5  ADC1_IRQHandler                      
00007ec5  AES_IRQHandler                       
00007ec5  CANFD0_IRQHandler                    
00007ec5  DAC0_IRQHandler                      
00007ec5  DMA_IRQHandler                       
00007ec5  Default_Handler                      
00007ec5  GROUP0_IRQHandler                    
00007ec5  HardFault_Handler                    
00007ec5  I2C0_IRQHandler                      
00007ec5  I2C1_IRQHandler                      
00007ec5  NMI_Handler                          
00007ec5  PendSV_Handler                       
00007ec5  RTC_IRQHandler                       
00007ec5  SPI0_IRQHandler                      
00007ec5  SPI1_IRQHandler                      
00007ec5  SVC_Handler                          
00007ec5  TIMA0_IRQHandler                     
00007ec5  TIMA1_IRQHandler                     
00007ec5  TIMG0_IRQHandler                     
00007ec5  TIMG12_IRQHandler                    
00007ec5  TIMG6_IRQHandler                     
00007ec5  TIMG7_IRQHandler                     
00007ec5  TIMG8_IRQHandler                     
00007ec5  UART0_IRQHandler                     
00007ec5  UART1_IRQHandler                     
00007ec5  UART2_IRQHandler                     
00007ec5  UART3_IRQHandler                     
00007ec8  C$$EXIT                              
00007ec9  HOSTexit                             
00007ed1  Reset_Handler                        
00007ee5  _system_pre_init                     
00008af0  __aeabi_ctype_table_                 
00008af0  __aeabi_ctype_table_C                
00008c60  test                                 
00008c88  reg                                  
00008d32  hw                                   
00008d80  __TI_Handler_Table_Base              
00008d8c  __TI_Handler_Table_Limit             
00008d94  __TI_CINIT_Base                      
00008da4  __TI_CINIT_Limit                     
00008da4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b8  __aeabi_errno                        
202004bc  delayTick                            
202004c4  uwTick                               
202004cf  Task_Flag                            
202004d2  Task_State                           
202004d3  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[304 symbols]
