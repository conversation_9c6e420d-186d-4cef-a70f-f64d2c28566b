******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 21:24:01 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007019


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008a78  00017588  R  X
  SRAM                  20200000   00008000  000006c5  0000793b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008a78   00008a78    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007b30   00007b30    r-x .text
  00007bf0    00007bf0    00000e40   00000e40    r-- .rodata
  00008a30    00008a30    00000048   00000048    r-- .cinit
20200000    20200000    000004c7   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000077   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007b30     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001efc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020f0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022dc    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000024b8    000001b0     Task.o (.text.Task_Start)
                  00002668    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002808    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000299a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000299c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002b24    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002c9c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e0c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002f50    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000308c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000031c0    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000032f4    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003424    00000128     Task_App.o (.text.Task_PID)
                  0000354c    00000128     inv_mpu.o (.text.mpu_init)
                  00003674    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003798    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000038b8    00000110     OLED.o (.text.OLED_Init)
                  000039c8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003ad4    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003bdc    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003ce0    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003de0    000000fc     PID_Param.o (.text.VelocityRing_PID_Realize)
                  00003edc    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003fc8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000040ac    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004190    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000426c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004344    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000441c    000000d4     inv_mpu.o (.text.set_int_enable)
                  000044f0    000000d0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000045c0    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004690    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004754    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004818    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000048d4    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000498c    000000b4     Task.o (.text.Task_Add)
                  00004a40    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004aec    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004b98    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004c3a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004c3c    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004cd8    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004d70    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004e08    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00004e9e    00000002     --HOLE-- [fill = 0]
                  00004ea0    00000094     MyConfig.o (.text.Car_Tracking)
                  00004f34    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00004fc0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000504c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000050d8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005164    00000084     Motor.o (.text.Load_Motor_PWM)
                  000051e8    00000084     Serial.o (.text.MyPrintf)
                  0000526c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000052f0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005374    00000084     main.o (.text.main)
                  000053f8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000547a    00000002     --HOLE-- [fill = 0]
                  0000547c    0000007c     PID_Param.o (.text.LocationRing_PID_Realize)
                  000054f8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005574    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000055e8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000055f0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005664    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000056d8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005748    0000006c     MyConfig.o (.text.LocationRing_Out)
                  000057b4    0000006c     Task_App.o (.text.Task_Init)
                  00005820    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000588a    00000002     --HOLE-- [fill = 0]
                  0000588c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000058f4    00000068     Motor.o (.text.read_encoder)
                  0000595c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000059c2    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005a28    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005a8c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005af0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005b52    00000002     --HOLE-- [fill = 0]
                  00005b54    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005bb6    00000002     --HOLE-- [fill = 0]
                  00005bb8    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005c18    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005c78    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005cd8    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005d38    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005d96    00000002     --HOLE-- [fill = 0]
                  00005d98    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005df4    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005e50    0000005c     Task_App.o (.text.Task_Encoder)
                  00005eac    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005f08    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005f64    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005fc0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006018    00000058     Serial.o (.text.Serial_Init)
                  00006070    00000058     Task_App.o (.text.Task_IdleFunction)
                  000060c8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006120    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006178    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000061ce    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006220    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006270    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000062c0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006310    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000635c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000063a8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000063f2    00000002     --HOLE-- [fill = 0]
                  000063f4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000643c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006484    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  000064cc    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006514    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006558    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  0000659c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000065e0    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006624    00000040     PID_Param.o (.text.PID_Param_Init)
                  00006664    00000040     MyConfig.o (.text.VelocityRing_Out)
                  000066a4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000066e4    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006724    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006764    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000067a4    0000003e     Task.o (.text.Task_CMP)
                  000067e2    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006820    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000685c    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006898    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000068d4    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006910    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000694c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006988    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000069c4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006a00    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006a3a    00000002     --HOLE-- [fill = 0]
                  00006a3c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006a76    00000002     --HOLE-- [fill = 0]
                  00006a78    00000038     Motor.o (.text.Motor_Start)
                  00006ab0    00000038     Task_App.o (.text.Task_Serial)
                  00006ae8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006b20    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006b54    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006b88    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006bbc    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006bf0    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006c24    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006c56    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006c88    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006cb8    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006ce8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006d18    00000030     Interrupt.o (.text.SysTick_Handler)
                  00006d48    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006d78    00000030            : vsnprintf.c.obj (.text._outs)
                  00006da8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006dd8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006e08    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006e34    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006e60    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006e88    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006eb0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006ed8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006f00    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006f28    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006f50    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006f78    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006fa0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00006fc8    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006ff0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007018    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007040    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007066    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000708c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000070b2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000070d8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000070fc    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00007120    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007144    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007166    00000002     --HOLE-- [fill = 0]
                  00007168    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007188    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000071a8    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  000071c8    00000020     SysTick.o (.text.Delay)
                  000071e8    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007208    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007226    00000002     --HOLE-- [fill = 0]
                  00007228    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007246    00000002     --HOLE-- [fill = 0]
                  00007248    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007264    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007280    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000729c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000072b8    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000072d4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000072f0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000730c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007328    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00007344    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007360    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000737c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007398    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000073b4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000073d0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000073ec    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007408    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007424    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007440    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000745c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007478    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007490    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000074a8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000074c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000074d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000074f0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007508    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007520    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007538    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007550    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007568    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007580    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007598    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000075b0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000075c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000075e0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000075f8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007610    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007628    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007640    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007658    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007670    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007688    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000076a0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000076b8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000076d0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000076e8    00000018     OLED.o (.text.DL_I2C_reset)
                  00007700    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007718    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007730    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007748    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007760    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007778    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007790    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000077a8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000077c0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000077d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000077f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007808    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007820    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007838    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007850    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007868    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007880    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007896    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000078ac    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000078c2    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000078d8    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000078ee    00000016     SysTick.o (.text.SysGetTick)
                  00007904    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007918    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000792c    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007940    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007954    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007968    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  0000797c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007990    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000079a4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000079b8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000079cc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000079e0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000079f4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007a08    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007a1c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007a30    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007a42    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007a54    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007a66    00000002     --HOLE-- [fill = 0]
                  00007a68    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007a78    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007a88    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007a98    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007aa8    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007ab8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007ac6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007ad4    0000000e     MPU6050.o (.text.tap_cb)
                  00007ae2    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007af0    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007afc    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007b08    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007b14    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007b1e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007b28    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007b38    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007b42    00000002     --HOLE-- [fill = 0]
                  00007b44    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007b54    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007b5e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007b68    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007b72    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007b7c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007b8c    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007b96    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007b9e    00000002     --HOLE-- [fill = 0]
                  00007ba0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007ba8    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007bae    00000002     --HOLE-- [fill = 0]
                  00007bb0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007bc0    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007bc6    00000006            : exit.c.obj (.text:abort)
                  00007bcc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007bd0    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007bd4    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007bd8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007bdc    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007bec    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00008a30    00000048     
                  00008a30    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00008a4f    00000001     --HOLE-- [fill = 0]
                  00008a50    0000000c     (__TI_handler_table)
                  00008a5c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008a64    00000010     (__TI_cinit_table)
                  00008a74    00000004     --HOLE-- [fill = 0]

.rodata    0    00007bf0    00000e40     
                  00007bf0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000087e6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000087f0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000088f1    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000088f8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008938    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008960    00000028     inv_mpu.o (.rodata.test)
                  00008988    0000001e     inv_mpu.o (.rodata.reg)
                  000089a6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000089a8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000089c0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000089d8    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000089e9    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000089fa    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008a07    00000001     --HOLE-- [fill = 0]
                  00008a08    0000000c     inv_mpu.o (.rodata.hw)
                  00008a14    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008a1c    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008a24    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008a28    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008a2b    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008a2d    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008a2f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000077     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004b4    00000004     SysTick.o (.data.delayTick)
                  202004b8    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004bc    00000004     SysTick.o (.data.uwTick)
                  202004c0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004c2    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004c3    00000001     Key_Led.o (.data.Task_Flag)
                  202004c4    00000001     Task.o (.data.Task_Num)
                  202004c5    00000001     Key_Led.o (.data.Task_State)
                  202004c6    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3274    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     640     32        6      
       Interrupt.o                    450     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1090    32        12     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Serial.o                       424     0         512    
       Task.o                         674     0         241    
       Task1.o                        744     0         9      
       Motor.o                        564     0         4      
       PID_Param.o                    440     0         72     
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7080    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       67        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31504   3905      1733   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008a64 records: 2, size/record: 8, table size: 16
	.data: load addr=00008a30, load size=0000001f bytes, run addr=20200450, run size=00000077 bytes, compression=lzss
	.bss: load addr=00008a5c, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008a50 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002809     00007b28     00007b26   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003fc9     00007b44     00007b40   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007b5c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007b70          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007b9c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007bc4          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   000039c9     00007b7c     00007b7a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002813     00007bb0     00007bac   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007bd6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007019     00007bdc     00007bd8   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007bcd  ADC0_IRQHandler                      
00007bcd  ADC1_IRQHandler                      
00007bcd  AES_IRQHandler                       
00007bd0  C$$EXIT                              
00007bcd  CANFD0_IRQHandler                    
00004ea1  Car_Tracking                         
00007bcd  DAC0_IRQHandler                      
00007b15  DL_Common_delayCycles                
00006311  DL_DMA_initChannel                   
00005d39  DL_I2C_fillControllerTXFIFO          
000068d5  DL_I2C_flushControllerTXFIFO         
000070b3  DL_I2C_setClockConfig                
00004191  DL_SYSCTL_configSYSPLL               
00005a29  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006515  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003bdd  DL_Timer_initFourCCPWMMode           
00007409  DL_Timer_setCaptCompUpdateMethod     
000077a9  DL_Timer_setCaptureCompareOutCtl     
00007a79  DL_Timer_setCaptureCompareValue      
00007425  DL_Timer_setClockConfig              
000063f5  DL_UART_init                         
00007a31  DL_UART_setClockConfig               
000071a9  DL_UART_transmitDataBlocking         
00007bcd  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00007bcd  Default_Handler                      
000071c9  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00007bcd  GROUP0_IRQHandler                    
000044f1  GROUP1_IRQHandler                    
00007bd1  HOSTexit                             
00007bcd  HardFault_Handler                    
00007bcd  I2C0_IRQHandler                      
00007bcd  I2C1_IRQHandler                      
00005821  I2C_OLED_Clear                       
00004cd9  I2C_OLED_WR_Byte                     
00005bb9  I2C_OLED_i2c_sda_unlock              
00006cb9  Interrupt_Init                       
00005165  Load_Motor_PWM                       
00005749  LocationRing_Out                     
0000547d  LocationRing_PID_Realize             
000070fd  LocationRing_VelocityRing_Control    
00002e0d  MPU6050_Init                         
00005d99  Motor_SetDirc                        
000056d9  Motor_SetPWM                         
00006a79  Motor_Start                          
000051e9  MyPrintf                             
00007bcd  NMI_Handler                          
000038b9  OLED_Init                            
202003ac  PID                                  
00006625  PID_Param_Init                       
202003f4  Param                                
00007bcd  PendSV_Handler                       
00007bcd  RTC_IRQHandler                       
00001885  Read_Quad                            
00007bd9  Reset_Handler                        
00007bcd  SPI0_IRQHandler                      
00007bcd  SPI1_IRQHandler                      
00007bcd  SVC_Handler                          
00006ce9  SYSCFG_DL_DMA_CH_RX_init             
00007851  SYSCFG_DL_DMA_CH_TX_init             
00007af1  SYSCFG_DL_DMA_init                   
000020f1  SYSCFG_DL_GPIO_init                  
00005fc1  SYSCFG_DL_I2C_MPU6050_init           
00005a8d  SYSCFG_DL_I2C_OLED_init              
00004f35  SYSCFG_DL_MotorFront_init            
00005df5  SYSCFG_DL_SYSCTL_init                
00007a89  SYSCFG_DL_SYSTICK_init               
0000526d  SYSCFG_DL_UART0_init                 
00006bbd  SYSCFG_DL_init                       
00004fc1  SYSCFG_DL_initPower                  
00006019  Serial_Init                          
20200000  Serial_RxData                        
000078ef  SysGetTick                           
00006d19  SysTick_Handler                      
00006fc9  SysTick_Increasment                  
00007afd  Sys_GetTick                          
00007bcd  TIMA0_IRQHandler                     
00007bcd  TIMA1_IRQHandler                     
00007bcd  TIMG0_IRQHandler                     
00007bcd  TIMG12_IRQHandler                    
00007bcd  TIMG6_IRQHandler                     
00007bcd  TIMG7_IRQHandler                     
00007bcd  TIMG8_IRQHandler                     
00007a43  TI_memcpy_small                      
00007ae3  TI_memset_small                      
000010ed  Task_1                               
0000498d  Task_Add                             
00005e51  Task_Encoder                         
202004c3  Task_Flag                            
00006071  Task_IdleFunction                    
000057b5  Task_Init                            
00003425  Task_PID                             
00006ab1  Task_Serial                          
000024b9  Task_Start                           
202004c5  Task_State                           
00007bcd  UART0_IRQHandler                     
00007bcd  UART1_IRQHandler                     
00007bcd  UART2_IRQHandler                     
00007bcd  UART3_IRQHandler                     
00006665  VelocityRing_Out                     
00003de1  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008a64  __TI_CINIT_Base                      
00008a74  __TI_CINIT_Limit                     
00008a74  __TI_CINIT_Warm                      
00008a50  __TI_Handler_Table_Base              
00008a5c  __TI_Handler_Table_Limit             
000069c5  __TI_auto_init_nobinit_nopinit       
000054f9  __TI_decompress_lzss                 
00007a55  __TI_decompress_none                 
000060c9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007aa9  __TI_zero_init                       
00002813  __adddf3                             
0000434f  __addsf3                             
000087f0  __aeabi_ctype_table_                 
000087f0  __aeabi_ctype_table_C                
000055f1  __aeabi_d2f                          
000063a9  __aeabi_d2iz                         
00002813  __aeabi_dadd                         
00005af1  __aeabi_dcmpeq                       
00005b2d  __aeabi_dcmpge                       
00005b41  __aeabi_dcmpgt                       
00005b19  __aeabi_dcmple                       
00005b05  __aeabi_dcmplt                       
000039c9  __aeabi_ddiv                         
00003fc9  __aeabi_dmul                         
00002809  __aeabi_dsub                         
202004b0  __aeabi_errno                        
000055e9  __aeabi_errno_addr                   
000066e5  __aeabi_f2d                          
00006ae9  __aeabi_f2iz                         
0000434f  __aeabi_fadd                         
00005b55  __aeabi_fcmpeq                       
00005b91  __aeabi_fcmpge                       
00005ba5  __aeabi_fcmpgt                       
00005b7d  __aeabi_fcmple                       
00005b69  __aeabi_fcmplt                       
000053f9  __aeabi_fdiv                         
0000504d  __aeabi_fmul                         
00004345  __aeabi_fsub                         
00006e35  __aeabi_i2d                          
0000694d  __aeabi_i2f                          
00006179  __aeabi_idiv                         
0000299b  __aeabi_idiv0                        
00006179  __aeabi_idivmod                      
00004c3b  __aeabi_ldiv0                        
00007229  __aeabi_llsl                         
00007121  __aeabi_lmul                         
00007b09  __aeabi_memclr                       
00007b09  __aeabi_memclr4                      
00007b09  __aeabi_memclr8                      
00007ba1  __aeabi_memcpy                       
00007ba1  __aeabi_memcpy4                      
00007ba1  __aeabi_memcpy8                      
00007ab9  __aeabi_memset                       
00007ab9  __aeabi_memset4                      
00007ab9  __aeabi_memset8                      
00006ff1  __aeabi_ui2f                         
000066a5  __aeabi_uidiv                        
000066a5  __aeabi_uidivmod                     
000079e1  __aeabi_uldivmod                     
00007229  __ashldi3                            
ffffffff  __binit__                            
0000588d  __cmpdf2                             
00006a01  __cmpsf2                             
000039c9  __divdf3                             
000053f9  __divsf3                             
0000588d  __eqdf2                              
00006a01  __eqsf2                              
000066e5  __extendsfdf2                        
000063a9  __fixdfsi                            
00006ae9  __fixsfsi                            
00006e35  __floatsidf                          
0000694d  __floatsisf                          
00006ff1  __floatunsisf                        
00005575  __gedf2                              
00006989  __gesf2                              
00005575  __gtdf2                              
00006989  __gtsf2                              
0000588d  __ledf2                              
00006a01  __lesf2                              
0000588d  __ltdf2                              
00006a01  __ltsf2                              
UNDEFED   __mpu_init                           
00003fc9  __muldf3                             
00007121  __muldi3                             
00006a3d  __muldsi3                            
0000504d  __mulsf3                             
0000588d  __nedf2                              
00006a01  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002809  __subdf3                             
00004345  __subsf3                             
000055f1  __truncdfsf2                         
00004b99  __udivmoddi4                         
00007019  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007bed  _system_pre_init                     
00007bc7  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000299d  atan2                                
0000299d  atan2l                               
00000df5  atanl                                
00006725  atoi                                 
ffffffff  binit                                
202004b4  delayTick                            
0000643d  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005c19  dmp_enable_gyro_cal                  
00006485  dmp_enable_lp_quat                   
0000745d  dmp_load_motion_driver_firmware      
00001efd  dmp_read_fifo                        
000079f5  dmp_register_android_orient_cb       
00007a09  dmp_register_tap_cb                  
00004d71  dmp_set_fifo_rate                    
00002b25  dmp_set_orientation                  
00006559  dmp_set_shake_reject_thresh          
00006c25  dmp_set_shake_reject_time            
00006c57  dmp_set_shake_reject_timeout         
000059c3  dmp_set_tap_axes                     
0000659d  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00006da9  dmp_set_tap_time                     
00006dd9  dmp_set_tap_time_multi               
202004c6  enable_group1_irq                    
00005ead  frexp                                
00005ead  frexpl                               
202002f0  gMotorFrontBackup                    
00008a08  hw                                   
00000000  interruptVectors                     
0000426d  ldexp                                
0000426d  ldexpl                               
00005375  main                                 
00007145  memccpy                              
000071e9  memcmp                               
20200427  more                                 
00005c79  mpu6050_i2c_sda_unlock               
00004819  mpu_configure_fifo                   
00005665  mpu_get_accel_fsr                    
00005cd9  mpu_get_gyro_fsr                     
00006bf1  mpu_get_sample_rate                  
0000354d  mpu_init                             
00003675  mpu_load_firmware                    
00003ce1  mpu_lp_accel_mode                    
00003ad5  mpu_read_fifo_stream                 
00004a41  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
000040ad  mpu_set_accel_fsr                    
00002669  mpu_set_bypass                       
000048d5  mpu_set_dmp_state                    
00004691  mpu_set_gyro_fsr                     
00004c3d  mpu_set_int_latched                  
000045c1  mpu_set_lpf                          
00003edd  mpu_set_sample_rate                  
000032f5  mpu_set_sensors                      
00004aed  mpu_write_mem                        
0000308d  mspm0_i2c_read                       
00004755  mspm0_i2c_write                      
000031c1  qsort                                
2020040c  quat                                 
000058f5  read_encoder                         
00008988  reg                                  
0000426d  scalbn                               
0000426d  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002c9d  sqrt                                 
00002c9d  sqrtl                                
2020044a  stop_time_cnt                        
00008960  test                                 
2020044c  time                                 
202004bc  uwTick                               
00006765  vsnprintf                            
00007a99  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001efd  dmp_read_fifo                        
000020f1  SYSCFG_DL_GPIO_init                  
000024b9  Task_Start                           
00002669  mpu_set_bypass                       
00002809  __aeabi_dsub                         
00002809  __subdf3                             
00002813  __adddf3                             
00002813  __aeabi_dadd                         
0000299b  __aeabi_idiv0                        
0000299d  atan2                                
0000299d  atan2l                               
00002b25  dmp_set_orientation                  
00002c9d  sqrt                                 
00002c9d  sqrtl                                
00002e0d  MPU6050_Init                         
0000308d  mspm0_i2c_read                       
000031c1  qsort                                
000032f5  mpu_set_sensors                      
00003425  Task_PID                             
0000354d  mpu_init                             
00003675  mpu_load_firmware                    
000038b9  OLED_Init                            
000039c9  __aeabi_ddiv                         
000039c9  __divdf3                             
00003ad5  mpu_read_fifo_stream                 
00003bdd  DL_Timer_initFourCCPWMMode           
00003ce1  mpu_lp_accel_mode                    
00003de1  VelocityRing_PID_Realize             
00003edd  mpu_set_sample_rate                  
00003fc9  __aeabi_dmul                         
00003fc9  __muldf3                             
000040ad  mpu_set_accel_fsr                    
00004191  DL_SYSCTL_configSYSPLL               
0000426d  ldexp                                
0000426d  ldexpl                               
0000426d  scalbn                               
0000426d  scalbnl                              
00004345  __aeabi_fsub                         
00004345  __subsf3                             
0000434f  __addsf3                             
0000434f  __aeabi_fadd                         
000044f1  GROUP1_IRQHandler                    
000045c1  mpu_set_lpf                          
00004691  mpu_set_gyro_fsr                     
00004755  mspm0_i2c_write                      
00004819  mpu_configure_fifo                   
000048d5  mpu_set_dmp_state                    
0000498d  Task_Add                             
00004a41  mpu_read_mem                         
00004aed  mpu_write_mem                        
00004b99  __udivmoddi4                         
00004c3b  __aeabi_ldiv0                        
00004c3d  mpu_set_int_latched                  
00004cd9  I2C_OLED_WR_Byte                     
00004d71  dmp_set_fifo_rate                    
00004ea1  Car_Tracking                         
00004f35  SYSCFG_DL_MotorFront_init            
00004fc1  SYSCFG_DL_initPower                  
0000504d  __aeabi_fmul                         
0000504d  __mulsf3                             
00005165  Load_Motor_PWM                       
000051e9  MyPrintf                             
0000526d  SYSCFG_DL_UART0_init                 
00005375  main                                 
000053f9  __aeabi_fdiv                         
000053f9  __divsf3                             
0000547d  LocationRing_PID_Realize             
000054f9  __TI_decompress_lzss                 
00005575  __gedf2                              
00005575  __gtdf2                              
000055e9  __aeabi_errno_addr                   
000055f1  __aeabi_d2f                          
000055f1  __truncdfsf2                         
00005665  mpu_get_accel_fsr                    
000056d9  Motor_SetPWM                         
00005749  LocationRing_Out                     
000057b5  Task_Init                            
00005821  I2C_OLED_Clear                       
0000588d  __cmpdf2                             
0000588d  __eqdf2                              
0000588d  __ledf2                              
0000588d  __ltdf2                              
0000588d  __nedf2                              
000058f5  read_encoder                         
000059c3  dmp_set_tap_axes                     
00005a29  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005a8d  SYSCFG_DL_I2C_OLED_init              
00005af1  __aeabi_dcmpeq                       
00005b05  __aeabi_dcmplt                       
00005b19  __aeabi_dcmple                       
00005b2d  __aeabi_dcmpge                       
00005b41  __aeabi_dcmpgt                       
00005b55  __aeabi_fcmpeq                       
00005b69  __aeabi_fcmplt                       
00005b7d  __aeabi_fcmple                       
00005b91  __aeabi_fcmpge                       
00005ba5  __aeabi_fcmpgt                       
00005bb9  I2C_OLED_i2c_sda_unlock              
00005c19  dmp_enable_gyro_cal                  
00005c79  mpu6050_i2c_sda_unlock               
00005cd9  mpu_get_gyro_fsr                     
00005d39  DL_I2C_fillControllerTXFIFO          
00005d99  Motor_SetDirc                        
00005df5  SYSCFG_DL_SYSCTL_init                
00005e51  Task_Encoder                         
00005ead  frexp                                
00005ead  frexpl                               
00005fc1  SYSCFG_DL_I2C_MPU6050_init           
00006019  Serial_Init                          
00006071  Task_IdleFunction                    
000060c9  __TI_ltoa                            
00006179  __aeabi_idiv                         
00006179  __aeabi_idivmod                      
00006311  DL_DMA_initChannel                   
000063a9  __aeabi_d2iz                         
000063a9  __fixdfsi                            
000063f5  DL_UART_init                         
0000643d  dmp_enable_6x_lp_quat                
00006485  dmp_enable_lp_quat                   
00006515  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006559  dmp_set_shake_reject_thresh          
0000659d  dmp_set_tap_count                    
00006625  PID_Param_Init                       
00006665  VelocityRing_Out                     
000066a5  __aeabi_uidiv                        
000066a5  __aeabi_uidivmod                     
000066e5  __aeabi_f2d                          
000066e5  __extendsfdf2                        
00006725  atoi                                 
00006765  vsnprintf                            
000068d5  DL_I2C_flushControllerTXFIFO         
0000694d  __aeabi_i2f                          
0000694d  __floatsisf                          
00006989  __gesf2                              
00006989  __gtsf2                              
000069c5  __TI_auto_init_nobinit_nopinit       
00006a01  __cmpsf2                             
00006a01  __eqsf2                              
00006a01  __lesf2                              
00006a01  __ltsf2                              
00006a01  __nesf2                              
00006a3d  __muldsi3                            
00006a79  Motor_Start                          
00006ab1  Task_Serial                          
00006ae9  __aeabi_f2iz                         
00006ae9  __fixsfsi                            
00006bbd  SYSCFG_DL_init                       
00006bf1  mpu_get_sample_rate                  
00006c25  dmp_set_shake_reject_time            
00006c57  dmp_set_shake_reject_timeout         
00006cb9  Interrupt_Init                       
00006ce9  SYSCFG_DL_DMA_CH_RX_init             
00006d19  SysTick_Handler                      
00006da9  dmp_set_tap_time                     
00006dd9  dmp_set_tap_time_multi               
00006e35  __aeabi_i2d                          
00006e35  __floatsidf                          
00006fc9  SysTick_Increasment                  
00006ff1  __aeabi_ui2f                         
00006ff1  __floatunsisf                        
00007019  _c_int00_noargs                      
000070b3  DL_I2C_setClockConfig                
000070fd  LocationRing_VelocityRing_Control    
00007121  __aeabi_lmul                         
00007121  __muldi3                             
00007145  memccpy                              
000071a9  DL_UART_transmitDataBlocking         
000071c9  Delay                                
000071e9  memcmp                               
00007229  __aeabi_llsl                         
00007229  __ashldi3                            
00007409  DL_Timer_setCaptCompUpdateMethod     
00007425  DL_Timer_setClockConfig              
0000745d  dmp_load_motion_driver_firmware      
000077a9  DL_Timer_setCaptureCompareOutCtl     
00007851  SYSCFG_DL_DMA_CH_TX_init             
000078ef  SysGetTick                           
000079e1  __aeabi_uldivmod                     
000079f5  dmp_register_android_orient_cb       
00007a09  dmp_register_tap_cb                  
00007a31  DL_UART_setClockConfig               
00007a43  TI_memcpy_small                      
00007a55  __TI_decompress_none                 
00007a79  DL_Timer_setCaptureCompareValue      
00007a89  SYSCFG_DL_SYSTICK_init               
00007a99  wcslen                               
00007aa9  __TI_zero_init                       
00007ab9  __aeabi_memset                       
00007ab9  __aeabi_memset4                      
00007ab9  __aeabi_memset8                      
00007ae3  TI_memset_small                      
00007af1  SYSCFG_DL_DMA_init                   
00007afd  Sys_GetTick                          
00007b09  __aeabi_memclr                       
00007b09  __aeabi_memclr4                      
00007b09  __aeabi_memclr8                      
00007b15  DL_Common_delayCycles                
00007ba1  __aeabi_memcpy                       
00007ba1  __aeabi_memcpy4                      
00007ba1  __aeabi_memcpy8                      
00007bc7  abort                                
00007bcd  ADC0_IRQHandler                      
00007bcd  ADC1_IRQHandler                      
00007bcd  AES_IRQHandler                       
00007bcd  CANFD0_IRQHandler                    
00007bcd  DAC0_IRQHandler                      
00007bcd  DMA_IRQHandler                       
00007bcd  Default_Handler                      
00007bcd  GROUP0_IRQHandler                    
00007bcd  HardFault_Handler                    
00007bcd  I2C0_IRQHandler                      
00007bcd  I2C1_IRQHandler                      
00007bcd  NMI_Handler                          
00007bcd  PendSV_Handler                       
00007bcd  RTC_IRQHandler                       
00007bcd  SPI0_IRQHandler                      
00007bcd  SPI1_IRQHandler                      
00007bcd  SVC_Handler                          
00007bcd  TIMA0_IRQHandler                     
00007bcd  TIMA1_IRQHandler                     
00007bcd  TIMG0_IRQHandler                     
00007bcd  TIMG12_IRQHandler                    
00007bcd  TIMG6_IRQHandler                     
00007bcd  TIMG7_IRQHandler                     
00007bcd  TIMG8_IRQHandler                     
00007bcd  UART0_IRQHandler                     
00007bcd  UART1_IRQHandler                     
00007bcd  UART2_IRQHandler                     
00007bcd  UART3_IRQHandler                     
00007bd0  C$$EXIT                              
00007bd1  HOSTexit                             
00007bd9  Reset_Handler                        
00007bed  _system_pre_init                     
000087f0  __aeabi_ctype_table_                 
000087f0  __aeabi_ctype_table_C                
00008960  test                                 
00008988  reg                                  
00008a08  hw                                   
00008a50  __TI_Handler_Table_Base              
00008a5c  __TI_Handler_Table_Limit             
00008a64  __TI_CINIT_Base                      
00008a74  __TI_CINIT_Limit                     
00008a74  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b0  __aeabi_errno                        
202004b4  delayTick                            
202004bc  uwTick                               
202004c3  Task_Flag                            
202004c5  Task_State                           
202004c6  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[303 symbols]
