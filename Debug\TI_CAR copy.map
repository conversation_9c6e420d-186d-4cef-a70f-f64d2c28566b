******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 16:15:09 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006c19


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008f08  000170f8  R  X
  SRAM                  20200000   00008000  000006a1  0000795f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008f08   00008f08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007780   00007780    r-x .text
  00007840    00007840    00001680   00001680    r-- .rodata
  00008ec0    00008ec0    00000048   00000048    r-- .cinit
20200000    20200000    000004a2   00000000    rw-
  20200000    20200000    00000437   00000000    rw- .bss
  20200438    20200438    0000006a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007780     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001fc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001e10    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002004    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021e0    000001b0     Task.o (.text.Task_Start)
                  00002390    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002530    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026c2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026c4    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000284c    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000029c4    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002b34    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002c78    00000144     PID.o (.text.PID_Prosc)
                  00002dbc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002ef8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000302c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003160    00000130     OLED.o (.text.OLED_ShowChar)
                  00003290    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000033c0    00000128     inv_mpu.o (.text.mpu_init)
                  000034e8    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  0000360c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  0000372c    00000110     OLED.o (.text.OLED_Init)
                  0000383c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003948    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003a50    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003b54    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003c54    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003d40    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003e24    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00003f08    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003fe4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000040bc    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004194    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004268    000000d0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004338    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004408    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000044cc    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004590    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  0000464c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004704    000000b4     Task.o (.text.Task_Add)
                  000047b8    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004864    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004910    000000a4     Task_App.o (.text.Task_OLED)
                  000049b4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004a56    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004a58    000000a0     Task_App.o (.text.Task_Init)
                  00004af8    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004b94    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004c2c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004cc4    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00004d5a    00000002     --HOLE-- [fill = 0]
                  00004d5c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00004de8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004e74    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004f00    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00004f8c    00000084     Serial.o (.text.MyPrintf)
                  00005010    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005094    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005118    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000519a    00000002     --HOLE-- [fill = 0]
                  0000519c    00000080     Task_App.o (.text.Task_Motor)
                  0000521c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005298    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000530c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005310    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005384    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000053f8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005468    0000006e     OLED.o (.text.OLED_ShowString)
                  000054d6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005540    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000055a8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000560e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005674    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000056d8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000573c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000579e    00000002     --HOLE-- [fill = 0]
                  000057a0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005802    00000002     --HOLE-- [fill = 0]
                  00005804    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005864    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  000058c4    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005924    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005984    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000059e2    00000002     --HOLE-- [fill = 0]
                  000059e4    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005a40    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005a9c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005af8    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005b54    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005bb0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00005c08    00000058     Serial.o (.text.Serial_Init)
                  00005c60    00000058     Task_App.o (.text.Task_IdleFunction)
                  00005cb8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00005d10    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005d68    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00005dbe    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00005e10    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00005e60    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00005eb0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00005f00    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00005f4c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00005f98    0000004c     OLED.o (.text.OLED_Printf)
                  00005fe4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000602e    00000002     --HOLE-- [fill = 0]
                  00006030    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006078    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000060c0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006108    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006150    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006194    00000044     PID.o (.text.PID_Init)
                  000061d8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  0000621c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006260    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000062a4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000062e4    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006324    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006364    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000063a4    0000003e     Task.o (.text.Task_CMP)
                  000063e2    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006420    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000645c    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006498    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000064d4    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006510    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000654c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006588    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000065c4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006600    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000663c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006676    00000002     --HOLE-- [fill = 0]
                  00006678    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000066b2    00000002     --HOLE-- [fill = 0]
                  000066b4    00000038     Motor.o (.text.Motor_Start)
                  000066ec    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006724    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006758    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000678c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000067c0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000067f4    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006828    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  0000685a    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  0000688c    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000068bc    00000030     Interrupt.o (.text.Interrupt_Init)
                  000068ec    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  0000691c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  0000694c    00000030            : vsnprintf.c.obj (.text._outs)
                  0000697c    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000069ac    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000069dc    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006a08    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006a34    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00006a60    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006a88    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006ab0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006ad8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006b00    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006b28    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006b50    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006b78    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006ba0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00006bc8    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006bf0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006c18    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006c40    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00006c66    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00006c8c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00006cb2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00006cd8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00006cfc    00000024     Task_App.o (.text.Task_Serial)
                  00006d20    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00006d44    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006d66    00000002     --HOLE-- [fill = 0]
                  00006d68    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006d88    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00006da8    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00006dc8    00000020     SysTick.o (.text.Delay)
                  00006de8    00000020     main.o (.text.main)
                  00006e08    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00006e28    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00006e46    00000002     --HOLE-- [fill = 0]
                  00006e48    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00006e66    00000002     --HOLE-- [fill = 0]
                  00006e68    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00006e84    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00006ea0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00006ebc    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00006ed8    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00006ef4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00006f10    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00006f2c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00006f48    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00006f64    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00006f80    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00006f9c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00006fb8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00006fd4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00006ff0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000700c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007028    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007044    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007060    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000707c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007098    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000070b0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000070c8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000070e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000070f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007110    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007128    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007140    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007158    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007170    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007188    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000071a0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000071b8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000071d0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000071e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007200    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007218    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007230    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007248    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007260    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007278    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007290    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000072a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000072c0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000072d8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000072f0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007308    00000018     OLED.o (.text.DL_I2C_reset)
                  00007320    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007338    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007350    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007368    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007380    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007398    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000073b0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000073c8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000073e0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000073f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007410    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007428    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007440    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007458    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007470    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007488    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000074a0    00000018            : vsprintf.c.obj (.text._outs)
                  000074b8    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000074ce    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000074e4    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000074fa    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007510    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007526    00000016     SysTick.o (.text.SysGetTick)
                  0000753c    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007550    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007564    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007578    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000758c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000075a0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000075b4    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000075c8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000075dc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000075f0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007604    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007618    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000762c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007640    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007654    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007668    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000767a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000768c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000769e    00000002     --HOLE-- [fill = 0]
                  000076a0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000076b0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000076c0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000076d0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000076e0    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000076f0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000076fe    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000770c    0000000e     MPU6050.o (.text.tap_cb)
                  0000771a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007728    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007734    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007740    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000774c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007756    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007760    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007770    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000777a    00000002     --HOLE-- [fill = 0]
                  0000777c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  0000778c    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007796    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000077a0    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000077aa    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000077b4    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000077c4    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  000077ce    0000000a     MPU6050.o (.text.android_orient_cb)
                  000077d8    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000077e0    00000008     Interrupt.o (.text.SysTick_Handler)
                  000077e8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000077f0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000077f8    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000077fe    00000002     --HOLE-- [fill = 0]
                  00007800    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007810    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007816    00000006            : exit.c.obj (.text:abort)
                  0000781c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007820    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007824    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007828    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007838    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000783c    00000004     --HOLE-- [fill = 0]

.cinit     0    00008ec0    00000048     
                  00008ec0    00000020     (.cinit..data.load) [load image, compression = lzss]
                  00008ee0    0000000c     (__TI_handler_table)
                  00008eec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008ef4    00000010     (__TI_cinit_table)
                  00008f04    00000004     --HOLE-- [fill = 0]

.rodata    0    00007840    00001680     
                  00007840    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008436    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00008a26    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00008c4e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008c50    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008d51    00000007     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008d58    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008d98    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008dc0    00000028     inv_mpu.o (.rodata.test)
                  00008de8    0000001e     inv_mpu.o (.rodata.reg)
                  00008e06    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008e08    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008e20    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008e38    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008e49    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008e5a    0000000c     inv_mpu.o (.rodata.hw)
                  00008e66    0000000c     Task_App.o (.rodata.str1.492715258893803702.1)
                  00008e72    0000000c     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00008e7e    0000000b     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00008e89    00000001     --HOLE-- [fill = 0]
                  00008e8a    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008e94    0000000a     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00008e9e    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008ea0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008ea8    00000008     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008eb0    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008eb6    00000005     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008ebb    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008ebe    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000437     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000058     (.common:Data_MotorPID)
                  20200404    00000010     (.common:quat)
                  20200414    00000006     (.common:Data_Accel)
                  2020041a    00000006     (.common:Data_Gyro)
                  20200420    00000004     (.common:Data_Pitch)
                  20200424    00000004     (.common:Data_Roll)
                  20200428    00000004     (.common:Data_Yaw)
                  2020042c    00000004     (.common:ExISR_Flag)
                  20200430    00000004     (.common:sensor_timestamp)
                  20200434    00000002     (.common:sensors)
                  20200436    00000001     (.common:more)

.data      0    20200438    0000006a     UNINITIALIZED
                  20200438    0000002c     inv_mpu.o (.data.st)
                  20200464    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200474    0000000e     MPU6050.o (.data.hal)
                  20200482    00000009     MPU6050.o (.data.gyro_orientation)
                  2020048b    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020048c    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200490    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200494    00000004     SysTick.o (.data.delayTick)
                  20200498    00000004     SysTick.o (.data.uwTick)
                  2020049c    00000002     Task_App.o (.data.Data_MotorPWM_Duty)
                  2020049e    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004a0    00000001     Task.o (.data.Task_Num)
                  202004a1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3150    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3190    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     576     71        96     
       Interrupt.o                    410     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         986     71        102    
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Serial.o                       424     0         512    
       Task.o                         674     0         241    
       PID.o                          392     0         0      
       Motor.o                        328     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         6246    2072      831    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8350    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       68        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   30560   6017      1697   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008ef4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008ec0, load size=00000020 bytes, run addr=20200438, run size=0000006a bytes, compression=lzss
	.bss: load addr=00008eec, load size=00000008 bytes, run addr=20200000, run size=00000437 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008ee0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002531     00007760     0000775e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003d41     0000777c     00007778   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007794          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000077a8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000077de          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007814          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   0000383d     000077b4     000077b2   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000253b     00007800     000077fc   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007822          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006c19     00007828     00007824   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000530d  ADC0_IRQHandler                      
0000530d  ADC1_IRQHandler                      
0000530d  AES_IRQHandler                       
0000781c  C$$EXIT                              
0000530d  CANFD0_IRQHandler                    
0000530d  DAC0_IRQHandler                      
0000774d  DL_Common_delayCycles                
00005f01  DL_DMA_initChannel                   
00005985  DL_I2C_fillControllerTXFIFO          
000064d5  DL_I2C_flushControllerTXFIFO         
00006cb3  DL_I2C_setClockConfig                
00003f09  DL_SYSCTL_configSYSPLL               
00005675  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006151  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003a51  DL_Timer_initFourCCPWMMode           
00007029  DL_Timer_setCaptCompUpdateMethod     
000073c9  DL_Timer_setCaptureCompareOutCtl     
000076b1  DL_Timer_setCaptureCompareValue      
00007045  DL_Timer_setClockConfig              
00006031  DL_UART_init                         
00007669  DL_UART_setClockConfig               
00006da9  DL_UART_transmitDataBlocking         
0000530d  DMA_IRQHandler                       
20200414  Data_Accel                           
2020041a  Data_Gyro                            
2020048c  Data_MotorEncoder                    
202003ac  Data_MotorPID                        
2020049c  Data_MotorPWM_Duty                   
20200420  Data_Pitch                           
20200424  Data_Roll                            
20200428  Data_Yaw                             
0000530d  Default_Handler                      
00006dc9  Delay                                
2020042c  ExISR_Flag                           
2020048b  Flag_MPU6050_Ready                   
0000530d  GROUP0_IRQHandler                    
00004269  GROUP1_IRQHandler                    
0000781d  HOSTexit                             
0000530d  HardFault_Handler                    
0000530d  I2C0_IRQHandler                      
0000530d  I2C1_IRQHandler                      
000054d7  I2C_OLED_Clear                       
0000654d  I2C_OLED_Set_Pos                     
00004b95  I2C_OLED_WR_Byte                     
00005805  I2C_OLED_i2c_sda_unlock              
000068bd  Interrupt_Init                       
00002b35  MPU6050_Init                         
000059e5  Motor_SetDirc                        
000053f9  Motor_SetPWM                         
000066b5  Motor_Start                          
00004f8d  MyPrintf                             
0000530d  NMI_Handler                          
0000372d  OLED_Init                            
00005f99  OLED_Printf                          
00003161  OLED_ShowChar                        
00005469  OLED_ShowString                      
00006195  PID_Init                             
00002c79  PID_Prosc                            
0000530d  PendSV_Handler                       
0000530d  RTC_IRQHandler                       
0000159d  Read_Quad                            
00007825  Reset_Handler                        
0000530d  SPI0_IRQHandler                      
0000530d  SPI1_IRQHandler                      
0000530d  SVC_Handler                          
000068ed  SYSCFG_DL_DMA_CH_RX_init             
00007471  SYSCFG_DL_DMA_CH_TX_init             
00007729  SYSCFG_DL_DMA_init                   
00001c15  SYSCFG_DL_GPIO_init                  
00005bb1  SYSCFG_DL_I2C_MPU6050_init           
000056d9  SYSCFG_DL_I2C_OLED_init              
00004d5d  SYSCFG_DL_MotorFront_init            
00005a41  SYSCFG_DL_SYSCTL_init                
000076c1  SYSCFG_DL_SYSTICK_init               
00005011  SYSCFG_DL_UART0_init                 
000067c1  SYSCFG_DL_init                       
00004de9  SYSCFG_DL_initPower                  
00005c09  Serial_Init                          
20200000  Serial_RxData                        
00007527  SysGetTick                           
000077e1  SysTick_Handler                      
00006bc9  SysTick_Increasment                  
00007735  Sys_GetTick                          
0000530d  TIMA0_IRQHandler                     
0000530d  TIMA1_IRQHandler                     
0000530d  TIMG0_IRQHandler                     
0000530d  TIMG12_IRQHandler                    
0000530d  TIMG6_IRQHandler                     
0000530d  TIMG7_IRQHandler                     
0000530d  TIMG8_IRQHandler                     
0000767b  TI_memcpy_small                      
0000771b  TI_memset_small                      
00004705  Task_Add                             
00005c61  Task_IdleFunction                    
00004a59  Task_Init                            
0000519d  Task_Motor                           
00004911  Task_OLED                            
00006cfd  Task_Serial                          
000021e1  Task_Start                           
0000530d  UART0_IRQHandler                     
0000530d  UART1_IRQHandler                     
0000530d  UART2_IRQHandler                     
0000530d  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008ef4  __TI_CINIT_Base                      
00008f04  __TI_CINIT_Limit                     
00008f04  __TI_CINIT_Warm                      
00008ee0  __TI_Handler_Table_Base              
00008eec  __TI_Handler_Table_Limit             
00006601  __TI_auto_init_nobinit_nopinit       
0000521d  __TI_decompress_lzss                 
0000768d  __TI_decompress_none                 
00005cb9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000076e1  __TI_zero_init                       
0000253b  __adddf3                             
000040c7  __addsf3                             
00008c50  __aeabi_ctype_table_                 
00008c50  __aeabi_ctype_table_C                
00005311  __aeabi_d2f                          
00005fe5  __aeabi_d2iz                         
0000253b  __aeabi_dadd                         
0000573d  __aeabi_dcmpeq                       
00005779  __aeabi_dcmpge                       
0000578d  __aeabi_dcmpgt                       
00005765  __aeabi_dcmple                       
00005751  __aeabi_dcmplt                       
0000383d  __aeabi_ddiv                         
00003d41  __aeabi_dmul                         
00002531  __aeabi_dsub                         
20200490  __aeabi_errno                        
000077e9  __aeabi_errno_addr                   
000062e5  __aeabi_f2d                          
000066ed  __aeabi_f2iz                         
000040c7  __aeabi_fadd                         
000057a1  __aeabi_fcmpeq                       
000057dd  __aeabi_fcmpge                       
000057f1  __aeabi_fcmpgt                       
000057c9  __aeabi_fcmple                       
000057b5  __aeabi_fcmplt                       
00005119  __aeabi_fdiv                         
00004e75  __aeabi_fmul                         
000040bd  __aeabi_fsub                         
00006a09  __aeabi_i2d                          
00006589  __aeabi_i2f                          
00005d69  __aeabi_idiv                         
000026c3  __aeabi_idiv0                        
00005d69  __aeabi_idivmod                      
00004a57  __aeabi_ldiv0                        
00006e49  __aeabi_llsl                         
00006d21  __aeabi_lmul                         
00007741  __aeabi_memclr                       
00007741  __aeabi_memclr4                      
00007741  __aeabi_memclr8                      
000077f1  __aeabi_memcpy                       
000077f1  __aeabi_memcpy4                      
000077f1  __aeabi_memcpy8                      
000076f1  __aeabi_memset                       
000076f1  __aeabi_memset4                      
000076f1  __aeabi_memset8                      
00006bf1  __aeabi_ui2f                         
000062a5  __aeabi_uidiv                        
000062a5  __aeabi_uidivmod                     
00007619  __aeabi_uldivmod                     
00006e49  __ashldi3                            
ffffffff  __binit__                            
00005541  __cmpdf2                             
0000663d  __cmpsf2                             
0000383d  __divdf3                             
00005119  __divsf3                             
00005541  __eqdf2                              
0000663d  __eqsf2                              
000062e5  __extendsfdf2                        
00005fe5  __fixdfsi                            
000066ed  __fixsfsi                            
00006a09  __floatsidf                          
00006589  __floatsisf                          
00006bf1  __floatunsisf                        
00005299  __gedf2                              
000065c5  __gesf2                              
00005299  __gtdf2                              
000065c5  __gtsf2                              
00005541  __ledf2                              
0000663d  __lesf2                              
00005541  __ltdf2                              
0000663d  __ltsf2                              
UNDEFED   __mpu_init                           
00003d41  __muldf3                             
00006d21  __muldi3                             
00006679  __muldsi3                            
00004e75  __mulsf3                             
00005541  __nedf2                              
0000663d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002531  __subdf3                             
000040bd  __subsf3                             
00005311  __truncdfsf2                         
000049b5  __udivmoddi4                         
00006c19  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007839  _system_pre_init                     
00007817  abort                                
00008a26  asc2_0806                            
00008436  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000026c5  atan2                                
000026c5  atan2l                               
00000df5  atanl                                
00006325  atoi                                 
ffffffff  binit                                
20200494  delayTick                            
00006079  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00005865  dmp_enable_gyro_cal                  
000060c1  dmp_enable_lp_quat                   
0000707d  dmp_load_motion_driver_firmware      
00001e11  dmp_read_fifo                        
0000762d  dmp_register_android_orient_cb       
00007641  dmp_register_tap_cb                  
00004c2d  dmp_set_fifo_rate                    
0000284d  dmp_set_orientation                  
000061d9  dmp_set_shake_reject_thresh          
00006829  dmp_set_shake_reject_time            
0000685b  dmp_set_shake_reject_timeout         
0000560f  dmp_set_tap_axes                     
0000621d  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
0000697d  dmp_set_tap_time                     
000069ad  dmp_set_tap_time_multi               
202004a1  enable_group1_irq                    
00005a9d  frexp                                
00005a9d  frexpl                               
202002f0  gMotorFrontBackup                    
00008e5a  hw                                   
00000000  interruptVectors                     
00003fe5  ldexp                                
00003fe5  ldexpl                               
00006de9  main                                 
00006d45  memccpy                              
00006e09  memcmp                               
20200436  more                                 
000058c5  mpu6050_i2c_sda_unlock               
00004591  mpu_configure_fifo                   
00005385  mpu_get_accel_fsr                    
00005925  mpu_get_gyro_fsr                     
000067f5  mpu_get_sample_rate                  
000033c1  mpu_init                             
000034e9  mpu_load_firmware                    
00003b55  mpu_lp_accel_mode                    
00003949  mpu_read_fifo_stream                 
000047b9  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00003e25  mpu_set_accel_fsr                    
00002391  mpu_set_bypass                       
0000464d  mpu_set_dmp_state                    
00004409  mpu_set_gyro_fsr                     
00004af9  mpu_set_int_latched                  
00004339  mpu_set_lpf                          
00003c55  mpu_set_sample_rate                  
00003291  mpu_set_sensors                      
00004865  mpu_write_mem                        
00002ef9  mspm0_i2c_read                       
000044cd  mspm0_i2c_write                      
0000302d  qsort                                
20200404  quat                                 
00008de8  reg                                  
00003fe5  scalbn                               
00003fe5  scalbnl                              
20200430  sensor_timestamp                     
20200434  sensors                              
000029c5  sqrt                                 
000029c5  sqrtl                                
00008dc0  test                                 
20200498  uwTick                               
00006365  vsnprintf                            
00006a35  vsprintf                             
000076d1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  SYSCFG_DL_GPIO_init                  
00001e11  dmp_read_fifo                        
000021e1  Task_Start                           
00002391  mpu_set_bypass                       
00002531  __aeabi_dsub                         
00002531  __subdf3                             
0000253b  __adddf3                             
0000253b  __aeabi_dadd                         
000026c3  __aeabi_idiv0                        
000026c5  atan2                                
000026c5  atan2l                               
0000284d  dmp_set_orientation                  
000029c5  sqrt                                 
000029c5  sqrtl                                
00002b35  MPU6050_Init                         
00002c79  PID_Prosc                            
00002ef9  mspm0_i2c_read                       
0000302d  qsort                                
00003161  OLED_ShowChar                        
00003291  mpu_set_sensors                      
000033c1  mpu_init                             
000034e9  mpu_load_firmware                    
0000372d  OLED_Init                            
0000383d  __aeabi_ddiv                         
0000383d  __divdf3                             
00003949  mpu_read_fifo_stream                 
00003a51  DL_Timer_initFourCCPWMMode           
00003b55  mpu_lp_accel_mode                    
00003c55  mpu_set_sample_rate                  
00003d41  __aeabi_dmul                         
00003d41  __muldf3                             
00003e25  mpu_set_accel_fsr                    
00003f09  DL_SYSCTL_configSYSPLL               
00003fe5  ldexp                                
00003fe5  ldexpl                               
00003fe5  scalbn                               
00003fe5  scalbnl                              
000040bd  __aeabi_fsub                         
000040bd  __subsf3                             
000040c7  __addsf3                             
000040c7  __aeabi_fadd                         
00004269  GROUP1_IRQHandler                    
00004339  mpu_set_lpf                          
00004409  mpu_set_gyro_fsr                     
000044cd  mspm0_i2c_write                      
00004591  mpu_configure_fifo                   
0000464d  mpu_set_dmp_state                    
00004705  Task_Add                             
000047b9  mpu_read_mem                         
00004865  mpu_write_mem                        
00004911  Task_OLED                            
000049b5  __udivmoddi4                         
00004a57  __aeabi_ldiv0                        
00004a59  Task_Init                            
00004af9  mpu_set_int_latched                  
00004b95  I2C_OLED_WR_Byte                     
00004c2d  dmp_set_fifo_rate                    
00004d5d  SYSCFG_DL_MotorFront_init            
00004de9  SYSCFG_DL_initPower                  
00004e75  __aeabi_fmul                         
00004e75  __mulsf3                             
00004f8d  MyPrintf                             
00005011  SYSCFG_DL_UART0_init                 
00005119  __aeabi_fdiv                         
00005119  __divsf3                             
0000519d  Task_Motor                           
0000521d  __TI_decompress_lzss                 
00005299  __gedf2                              
00005299  __gtdf2                              
0000530d  ADC0_IRQHandler                      
0000530d  ADC1_IRQHandler                      
0000530d  AES_IRQHandler                       
0000530d  CANFD0_IRQHandler                    
0000530d  DAC0_IRQHandler                      
0000530d  DMA_IRQHandler                       
0000530d  Default_Handler                      
0000530d  GROUP0_IRQHandler                    
0000530d  HardFault_Handler                    
0000530d  I2C0_IRQHandler                      
0000530d  I2C1_IRQHandler                      
0000530d  NMI_Handler                          
0000530d  PendSV_Handler                       
0000530d  RTC_IRQHandler                       
0000530d  SPI0_IRQHandler                      
0000530d  SPI1_IRQHandler                      
0000530d  SVC_Handler                          
0000530d  TIMA0_IRQHandler                     
0000530d  TIMA1_IRQHandler                     
0000530d  TIMG0_IRQHandler                     
0000530d  TIMG12_IRQHandler                    
0000530d  TIMG6_IRQHandler                     
0000530d  TIMG7_IRQHandler                     
0000530d  TIMG8_IRQHandler                     
0000530d  UART0_IRQHandler                     
0000530d  UART1_IRQHandler                     
0000530d  UART2_IRQHandler                     
0000530d  UART3_IRQHandler                     
00005311  __aeabi_d2f                          
00005311  __truncdfsf2                         
00005385  mpu_get_accel_fsr                    
000053f9  Motor_SetPWM                         
00005469  OLED_ShowString                      
000054d7  I2C_OLED_Clear                       
00005541  __cmpdf2                             
00005541  __eqdf2                              
00005541  __ledf2                              
00005541  __ltdf2                              
00005541  __nedf2                              
0000560f  dmp_set_tap_axes                     
00005675  DL_SYSCTL_setHFCLKSourceHFXTParams   
000056d9  SYSCFG_DL_I2C_OLED_init              
0000573d  __aeabi_dcmpeq                       
00005751  __aeabi_dcmplt                       
00005765  __aeabi_dcmple                       
00005779  __aeabi_dcmpge                       
0000578d  __aeabi_dcmpgt                       
000057a1  __aeabi_fcmpeq                       
000057b5  __aeabi_fcmplt                       
000057c9  __aeabi_fcmple                       
000057dd  __aeabi_fcmpge                       
000057f1  __aeabi_fcmpgt                       
00005805  I2C_OLED_i2c_sda_unlock              
00005865  dmp_enable_gyro_cal                  
000058c5  mpu6050_i2c_sda_unlock               
00005925  mpu_get_gyro_fsr                     
00005985  DL_I2C_fillControllerTXFIFO          
000059e5  Motor_SetDirc                        
00005a41  SYSCFG_DL_SYSCTL_init                
00005a9d  frexp                                
00005a9d  frexpl                               
00005bb1  SYSCFG_DL_I2C_MPU6050_init           
00005c09  Serial_Init                          
00005c61  Task_IdleFunction                    
00005cb9  __TI_ltoa                            
00005d69  __aeabi_idiv                         
00005d69  __aeabi_idivmod                      
00005f01  DL_DMA_initChannel                   
00005f99  OLED_Printf                          
00005fe5  __aeabi_d2iz                         
00005fe5  __fixdfsi                            
00006031  DL_UART_init                         
00006079  dmp_enable_6x_lp_quat                
000060c1  dmp_enable_lp_quat                   
00006151  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006195  PID_Init                             
000061d9  dmp_set_shake_reject_thresh          
0000621d  dmp_set_tap_count                    
000062a5  __aeabi_uidiv                        
000062a5  __aeabi_uidivmod                     
000062e5  __aeabi_f2d                          
000062e5  __extendsfdf2                        
00006325  atoi                                 
00006365  vsnprintf                            
000064d5  DL_I2C_flushControllerTXFIFO         
0000654d  I2C_OLED_Set_Pos                     
00006589  __aeabi_i2f                          
00006589  __floatsisf                          
000065c5  __gesf2                              
000065c5  __gtsf2                              
00006601  __TI_auto_init_nobinit_nopinit       
0000663d  __cmpsf2                             
0000663d  __eqsf2                              
0000663d  __lesf2                              
0000663d  __ltsf2                              
0000663d  __nesf2                              
00006679  __muldsi3                            
000066b5  Motor_Start                          
000066ed  __aeabi_f2iz                         
000066ed  __fixsfsi                            
000067c1  SYSCFG_DL_init                       
000067f5  mpu_get_sample_rate                  
00006829  dmp_set_shake_reject_time            
0000685b  dmp_set_shake_reject_timeout         
000068bd  Interrupt_Init                       
000068ed  SYSCFG_DL_DMA_CH_RX_init             
0000697d  dmp_set_tap_time                     
000069ad  dmp_set_tap_time_multi               
00006a09  __aeabi_i2d                          
00006a09  __floatsidf                          
00006a35  vsprintf                             
00006bc9  SysTick_Increasment                  
00006bf1  __aeabi_ui2f                         
00006bf1  __floatunsisf                        
00006c19  _c_int00_noargs                      
00006cb3  DL_I2C_setClockConfig                
00006cfd  Task_Serial                          
00006d21  __aeabi_lmul                         
00006d21  __muldi3                             
00006d45  memccpy                              
00006da9  DL_UART_transmitDataBlocking         
00006dc9  Delay                                
00006de9  main                                 
00006e09  memcmp                               
00006e49  __aeabi_llsl                         
00006e49  __ashldi3                            
00007029  DL_Timer_setCaptCompUpdateMethod     
00007045  DL_Timer_setClockConfig              
0000707d  dmp_load_motion_driver_firmware      
000073c9  DL_Timer_setCaptureCompareOutCtl     
00007471  SYSCFG_DL_DMA_CH_TX_init             
00007527  SysGetTick                           
00007619  __aeabi_uldivmod                     
0000762d  dmp_register_android_orient_cb       
00007641  dmp_register_tap_cb                  
00007669  DL_UART_setClockConfig               
0000767b  TI_memcpy_small                      
0000768d  __TI_decompress_none                 
000076b1  DL_Timer_setCaptureCompareValue      
000076c1  SYSCFG_DL_SYSTICK_init               
000076d1  wcslen                               
000076e1  __TI_zero_init                       
000076f1  __aeabi_memset                       
000076f1  __aeabi_memset4                      
000076f1  __aeabi_memset8                      
0000771b  TI_memset_small                      
00007729  SYSCFG_DL_DMA_init                   
00007735  Sys_GetTick                          
00007741  __aeabi_memclr                       
00007741  __aeabi_memclr4                      
00007741  __aeabi_memclr8                      
0000774d  DL_Common_delayCycles                
000077e1  SysTick_Handler                      
000077e9  __aeabi_errno_addr                   
000077f1  __aeabi_memcpy                       
000077f1  __aeabi_memcpy4                      
000077f1  __aeabi_memcpy8                      
00007817  abort                                
0000781c  C$$EXIT                              
0000781d  HOSTexit                             
00007825  Reset_Handler                        
00007839  _system_pre_init                     
00008436  asc2_1608                            
00008a26  asc2_0806                            
00008c50  __aeabi_ctype_table_                 
00008c50  __aeabi_ctype_table_C                
00008dc0  test                                 
00008de8  reg                                  
00008e5a  hw                                   
00008ee0  __TI_Handler_Table_Base              
00008eec  __TI_Handler_Table_Limit             
00008ef4  __TI_CINIT_Base                      
00008f04  __TI_CINIT_Limit                     
00008f04  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  Data_MotorPID                        
20200404  quat                                 
20200414  Data_Accel                           
2020041a  Data_Gyro                            
20200420  Data_Pitch                           
20200424  Data_Roll                            
20200428  Data_Yaw                             
2020042c  ExISR_Flag                           
20200430  sensor_timestamp                     
20200434  sensors                              
20200436  more                                 
2020048b  Flag_MPU6050_Ready                   
2020048c  Data_MotorEncoder                    
20200490  __aeabi_errno                        
20200494  delayTick                            
20200498  uwTick                               
2020049c  Data_MotorPWM_Duty                   
202004a1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[297 symbols]
