******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 22 20:11:15 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006c35


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008690  00017970  R  X
  SRAM                  20200000   00008000  000006bc  00007944  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008690   00008690    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007750   00007750    r-x .text
  00007810    00007810    00000e40   00000e40    r-- .rodata
  00008650    00008650    00000040   00000040    r-- .cinit
20200000    20200000    000004be   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    0000006e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007750     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001ff4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021d0    000001b0     Task.o (.text.Task_Start)
                  00002380    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002520    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026b2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026b4    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000283c    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000029b4    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002b24    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002c68    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002da4    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002ed8    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000300c    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  0000313c    00000128     Interrupt.o (.text.SysTick_Handler)
                  00003264    00000128     inv_mpu.o (.text.mpu_init)
                  0000338c    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000034b0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000035d0    00000110     OLED.o (.text.OLED_Init)
                  000036e0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000037ec    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  000038f4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000039f8    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003af8    000000fc     PID_Param.o (.text.VelocityRing_PID_Realize)
                  00003bf4    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003ce0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003dc4    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00003ea8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003f84    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000405c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004134    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004208    000000d0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000042d8    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000043a8    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  0000446c    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004530    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000045ec    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  000046a4    000000b4     Task.o (.text.Task_Add)
                  00004758    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004804    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  000048b0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004952    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004954    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  000049f0    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004a88    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004b20    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00004bb6    00000002     --HOLE-- [fill = 0]
                  00004bb8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00004c44    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004cd0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004d5c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00004de8    00000084     Motor.o (.text.Load_Motor_PWM)
                  00004e6c    00000084     Serial.o (.text.MyPrintf)
                  00004ef0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00004f74    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00004ff8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000507a    00000002     --HOLE-- [fill = 0]
                  0000507c    0000007c     PID_Param.o (.text.LocationRing_PID_Realize)
                  000050f8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005174    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000051e8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000051f0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005264    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000052d8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005348    0000006c     MyConfig.o (.text.LocationRing_Out)
                  000053b4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000541e    00000002     --HOLE-- [fill = 0]
                  00005420    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005488    00000068     main.o (.text.main)
                  000054f0    00000068     Motor.o (.text.read_encoder)
                  00005558    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000055be    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005624    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005688    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000056ec    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000574e    00000002     --HOLE-- [fill = 0]
                  00005750    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000057b2    00000002     --HOLE-- [fill = 0]
                  000057b4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005814    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005874    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000058d4    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005934    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005992    00000002     --HOLE-- [fill = 0]
                  00005994    0000005c     Motor.o (.text.Motor_SetDirc)
                  000059f0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005a4c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005aa8    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005b04    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00005b60    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00005bb8    00000058     Serial.o (.text.Serial_Init)
                  00005c10    00000058     Task_App.o (.text.Task_IdleFunction)
                  00005c68    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00005cc0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005d18    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00005d6e    00000002     --HOLE-- [fill = 0]
                  00005d70    00000054     Task_App.o (.text.Task_Encoder)
                  00005dc4    00000054     Task_App.o (.text.Task_Init)
                  00005e18    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00005e6a    00000002     --HOLE-- [fill = 0]
                  00005e6c    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00005ebc    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00005f0c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00005f5c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00005fa8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00005ff4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000603e    00000002     --HOLE-- [fill = 0]
                  00006040    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006088    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000060d0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006118    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006160    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000061a4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000061e8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  0000622c    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006270    00000040     PID_Param.o (.text.PID_Param_Init)
                  000062b0    00000040     MyConfig.o (.text.VelocityRing_Out)
                  000062f0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006330    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006370    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000063b0    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000063f0    0000003e     Task.o (.text.Task_CMP)
                  0000642e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  0000646c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000064a8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000064e4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006520    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000655c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006598    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000065d4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006610    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000664c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006686    00000002     --HOLE-- [fill = 0]
                  00006688    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000066c2    00000002     --HOLE-- [fill = 0]
                  000066c4    00000038     Motor.o (.text.Motor_Start)
                  000066fc    00000038     Task_App.o (.text.Task_Serial)
                  00006734    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000676c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000067a0    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000067d4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006808    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000683c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006870    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000068a2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000068d4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006904    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006934    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006964    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006994    00000030            : vsnprintf.c.obj (.text._outs)
                  000069c4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000069f4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006a24    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006a50    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006a7c    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006aa4    00000028     OLED.o (.text.DL_Common_updateReg)
                  00006acc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006af4    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006b1c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006b44    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006b6c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006b94    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006bbc    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00006be4    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006c0c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006c34    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006c5c    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00006c82    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00006ca8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00006cce    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00006cf4    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00006d18    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00006d3c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00006d60    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006d82    00000002     --HOLE-- [fill = 0]
                  00006d84    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006da4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00006dc4    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00006de4    00000020     SysTick.o (.text.Delay)
                  00006e04    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00006e24    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00006e42    00000002     --HOLE-- [fill = 0]
                  00006e44    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00006e62    00000002     --HOLE-- [fill = 0]
                  00006e64    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00006e80    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00006e9c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00006eb8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00006ed4    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00006ef0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00006f0c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00006f28    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00006f44    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00006f60    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00006f7c    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00006f98    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00006fb4    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00006fd0    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00006fec    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007008    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007024    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007040    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000705c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007078    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007094    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000070ac    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000070c4    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000070dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000070f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000710c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007124    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  0000713c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007154    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000716c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007184    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000719c    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000071b4    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000071cc    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000071e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000071fc    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007214    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  0000722c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007244    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000725c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007274    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  0000728c    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000072a4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000072bc    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000072d4    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000072ec    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007304    00000018     OLED.o (.text.DL_I2C_reset)
                  0000731c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007334    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000734c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007364    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  0000737c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007394    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000073ac    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000073c4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000073dc    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000073f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000740c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007424    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000743c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007454    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000746c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007484    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  0000749c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000074b2    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000074c8    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000074de    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000074f4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000750a    00000016     SysTick.o (.text.SysGetTick)
                  00007520    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007534    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007548    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000755c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007570    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007584    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007598    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000075ac    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000075c0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000075d4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000075e8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000075fc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007610    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007624    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007638    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000764c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000765e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007670    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007682    00000002     --HOLE-- [fill = 0]
                  00007684    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007694    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000076a4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000076b4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000076c4    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000076d4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000076e2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000076f0    0000000e     MPU6050.o (.text.tap_cb)
                  000076fe    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  0000770c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007718    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007724    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007730    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000773a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007744    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007754    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000775e    00000002     --HOLE-- [fill = 0]
                  00007760    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007770    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000777a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007784    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000778e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007798    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000077a8    0000000a     MPU6050.o (.text.android_orient_cb)
                  000077b2    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000077ba    00000002     --HOLE-- [fill = 0]
                  000077bc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000077c4    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000077ca    00000002     --HOLE-- [fill = 0]
                  000077cc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000077dc    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000077e2    00000006            : exit.c.obj (.text:abort)
                  000077e8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000077ec    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000077f0    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000077f4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000077f8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007808    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000780c    00000004     --HOLE-- [fill = 0]

.cinit     0    00008650    00000040     
                  00008650    0000001c     (.cinit..data.load) [load image, compression = lzss]
                  0000866c    0000000c     (__TI_handler_table)
                  00008678    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008680    00000010     (__TI_cinit_table)

.rodata    0    00007810    00000e40     
                  00007810    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008406    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008410    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008511    00000007     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008518    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008558    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008580    00000028     inv_mpu.o (.rodata.test)
                  000085a8    0000001e     inv_mpu.o (.rodata.reg)
                  000085c6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000085c8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000085e0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000085f8    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008609    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000861a    0000000d     Task_App.o (.rodata.str1.492715258893803702.1)
                  00008627    00000001     --HOLE-- [fill = 0]
                  00008628    0000000c     inv_mpu.o (.rodata.hw)
                  00008634    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  0000863c    00000008     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008644    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008647    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008649    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000864b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    0000006e     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004a4    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004a8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004ac    00000004     SysTick.o (.data.delayTick)
                  202004b0    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004b4    00000004     SysTick.o (.data.uwTick)
                  202004b8    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ba    00000001     Key_Led.o (.data.Task_Flag)
                  202004bb    00000001     Task.o (.data.Task_Num)
                  202004bc    00000001     Key_Led.o (.data.Task_State)
                  202004bd    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         104     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3246    307       188    
                                                               
    .\APP\Src\
       Interrupt.o                    698     0         6      
       Task_App.o                     312     28        6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1010    28        12     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Serial.o                       424     0         512    
       Task.o                         674     0         241    
       Motor.o                        564     0         4      
       PID_Param.o                    440     0         72     
       MyConfig.o                     208     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         6188    0         948    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       64        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   30504   3898      1724   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008680 records: 2, size/record: 8, table size: 16
	.data: load addr=00008650, load size=0000001c bytes, run addr=20200450, run size=0000006e bytes, compression=lzss
	.bss: load addr=00008678, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000866c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002521     00007744     00007742   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003ce1     00007760     0000775c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007778          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000778c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000077b8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000077e0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   000036e1     00007798     00007796   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000252b     000077cc     000077c8   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000077f2          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006c35     000077f8     000077f4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000077e9  ADC0_IRQHandler                      
000077e9  ADC1_IRQHandler                      
000077e9  AES_IRQHandler                       
000077ec  C$$EXIT                              
000077e9  CANFD0_IRQHandler                    
000077e9  DAC0_IRQHandler                      
00007731  DL_Common_delayCycles                
00005f5d  DL_DMA_initChannel                   
00005935  DL_I2C_fillControllerTXFIFO          
00006521  DL_I2C_flushControllerTXFIFO         
00006ccf  DL_I2C_setClockConfig                
00003ea9  DL_SYSCTL_configSYSPLL               
00005625  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006161  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000038f5  DL_Timer_initFourCCPWMMode           
00007025  DL_Timer_setCaptCompUpdateMethod     
000073c5  DL_Timer_setCaptureCompareOutCtl     
00007695  DL_Timer_setCaptureCompareValue      
00007041  DL_Timer_setClockConfig              
00006041  DL_UART_init                         
0000764d  DL_UART_setClockConfig               
00006dc5  DL_UART_transmitDataBlocking         
000077e9  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004a4  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
000077e9  Default_Handler                      
00006de5  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004a3  Flag_MPU6050_Ready                   
000077e9  GROUP0_IRQHandler                    
00004209  GROUP1_IRQHandler                    
000077ed  HOSTexit                             
000077e9  HardFault_Handler                    
000077e9  I2C0_IRQHandler                      
000077e9  I2C1_IRQHandler                      
000053b5  I2C_OLED_Clear                       
000049f1  I2C_OLED_WR_Byte                     
000057b5  I2C_OLED_i2c_sda_unlock              
00006905  Interrupt_Init                       
00004de9  Load_Motor_PWM                       
00005349  LocationRing_Out                     
0000507d  LocationRing_PID_Realize             
00006d19  LocationRing_VelocityRing_Control    
00002b25  MPU6050_Init                         
00005995  Motor_SetDirc                        
000052d9  Motor_SetPWM                         
000066c5  Motor_Start                          
00004e6d  MyPrintf                             
000077e9  NMI_Handler                          
000035d1  OLED_Init                            
202003ac  PID                                  
00006271  PID_Param_Init                       
202003f4  Param                                
000077e9  PendSV_Handler                       
000077e9  RTC_IRQHandler                       
0000159d  Read_Quad                            
000077f5  Reset_Handler                        
000077e9  SPI0_IRQHandler                      
000077e9  SPI1_IRQHandler                      
000077e9  SVC_Handler                          
00006935  SYSCFG_DL_DMA_CH_RX_init             
0000746d  SYSCFG_DL_DMA_CH_TX_init             
0000770d  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
00005b61  SYSCFG_DL_I2C_MPU6050_init           
00005689  SYSCFG_DL_I2C_OLED_init              
00004bb9  SYSCFG_DL_MotorFront_init            
000059f1  SYSCFG_DL_SYSCTL_init                
000076a5  SYSCFG_DL_SYSTICK_init               
00004ef1  SYSCFG_DL_UART0_init                 
00006809  SYSCFG_DL_init                       
00004c45  SYSCFG_DL_initPower                  
00005bb9  Serial_Init                          
20200000  Serial_RxData                        
0000750b  SysGetTick                           
0000313d  SysTick_Handler                      
00006be5  SysTick_Increasment                  
00007719  Sys_GetTick                          
000077e9  TIMA0_IRQHandler                     
000077e9  TIMA1_IRQHandler                     
000077e9  TIMG0_IRQHandler                     
000077e9  TIMG12_IRQHandler                    
000077e9  TIMG6_IRQHandler                     
000077e9  TIMG7_IRQHandler                     
000077e9  TIMG8_IRQHandler                     
0000765f  TI_memcpy_small                      
000076ff  TI_memset_small                      
000046a5  Task_Add                             
00005d71  Task_Encoder                         
202004ba  Task_Flag                            
00005c11  Task_IdleFunction                    
00005dc5  Task_Init                            
000066fd  Task_Serial                          
000021d1  Task_Start                           
202004bc  Task_State                           
000077e9  UART0_IRQHandler                     
000077e9  UART1_IRQHandler                     
000077e9  UART2_IRQHandler                     
000077e9  UART3_IRQHandler                     
000062b1  VelocityRing_Out                     
00003af9  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008680  __TI_CINIT_Base                      
00008690  __TI_CINIT_Limit                     
00008690  __TI_CINIT_Warm                      
0000866c  __TI_Handler_Table_Base              
00008678  __TI_Handler_Table_Limit             
00006611  __TI_auto_init_nobinit_nopinit       
000050f9  __TI_decompress_lzss                 
00007671  __TI_decompress_none                 
00005c69  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000076c5  __TI_zero_init                       
0000252b  __adddf3                             
00004067  __addsf3                             
00008410  __aeabi_ctype_table_                 
00008410  __aeabi_ctype_table_C                
000051f1  __aeabi_d2f                          
00005ff5  __aeabi_d2iz                         
0000252b  __aeabi_dadd                         
000056ed  __aeabi_dcmpeq                       
00005729  __aeabi_dcmpge                       
0000573d  __aeabi_dcmpgt                       
00005715  __aeabi_dcmple                       
00005701  __aeabi_dcmplt                       
000036e1  __aeabi_ddiv                         
00003ce1  __aeabi_dmul                         
00002521  __aeabi_dsub                         
202004a8  __aeabi_errno                        
000051e9  __aeabi_errno_addr                   
00006331  __aeabi_f2d                          
00006735  __aeabi_f2iz                         
00004067  __aeabi_fadd                         
00005751  __aeabi_fcmpeq                       
0000578d  __aeabi_fcmpge                       
000057a1  __aeabi_fcmpgt                       
00005779  __aeabi_fcmple                       
00005765  __aeabi_fcmplt                       
00004ff9  __aeabi_fdiv                         
00004cd1  __aeabi_fmul                         
0000405d  __aeabi_fsub                         
00006a51  __aeabi_i2d                          
00006599  __aeabi_i2f                          
00005d19  __aeabi_idiv                         
000026b3  __aeabi_idiv0                        
00005d19  __aeabi_idivmod                      
00004953  __aeabi_ldiv0                        
00006e45  __aeabi_llsl                         
00006d3d  __aeabi_lmul                         
00007725  __aeabi_memclr                       
00007725  __aeabi_memclr4                      
00007725  __aeabi_memclr8                      
000077bd  __aeabi_memcpy                       
000077bd  __aeabi_memcpy4                      
000077bd  __aeabi_memcpy8                      
000076d5  __aeabi_memset                       
000076d5  __aeabi_memset4                      
000076d5  __aeabi_memset8                      
00006c0d  __aeabi_ui2f                         
000062f1  __aeabi_uidiv                        
000062f1  __aeabi_uidivmod                     
000075fd  __aeabi_uldivmod                     
00006e45  __ashldi3                            
ffffffff  __binit__                            
00005421  __cmpdf2                             
0000664d  __cmpsf2                             
000036e1  __divdf3                             
00004ff9  __divsf3                             
00005421  __eqdf2                              
0000664d  __eqsf2                              
00006331  __extendsfdf2                        
00005ff5  __fixdfsi                            
00006735  __fixsfsi                            
00006a51  __floatsidf                          
00006599  __floatsisf                          
00006c0d  __floatunsisf                        
00005175  __gedf2                              
000065d5  __gesf2                              
00005175  __gtdf2                              
000065d5  __gtsf2                              
00005421  __ledf2                              
0000664d  __lesf2                              
00005421  __ltdf2                              
0000664d  __ltsf2                              
UNDEFED   __mpu_init                           
00003ce1  __muldf3                             
00006d3d  __muldi3                             
00006689  __muldsi3                            
00004cd1  __mulsf3                             
00005421  __nedf2                              
0000664d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002521  __subdf3                             
0000405d  __subsf3                             
000051f1  __truncdfsf2                         
000048b1  __udivmoddi4                         
00006c35  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007809  _system_pre_init                     
000077e3  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000026b5  atan2                                
000026b5  atan2l                               
00000df5  atanl                                
00006371  atoi                                 
ffffffff  binit                                
202004ac  delayTick                            
00006089  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00005815  dmp_enable_gyro_cal                  
000060d1  dmp_enable_lp_quat                   
00007079  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
00007611  dmp_register_android_orient_cb       
00007625  dmp_register_tap_cb                  
00004a89  dmp_set_fifo_rate                    
0000283d  dmp_set_orientation                  
000061a5  dmp_set_shake_reject_thresh          
00006871  dmp_set_shake_reject_time            
000068a3  dmp_set_shake_reject_timeout         
000055bf  dmp_set_tap_axes                     
000061e9  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
000069c5  dmp_set_tap_time                     
000069f5  dmp_set_tap_time_multi               
202004bd  enable_group1_irq                    
00005a4d  frexp                                
00005a4d  frexpl                               
202002f0  gMotorFrontBackup                    
00008628  hw                                   
00000000  interruptVectors                     
00003f85  ldexp                                
00003f85  ldexpl                               
00005489  main                                 
00006d61  memccpy                              
00006e05  memcmp                               
20200427  more                                 
00005875  mpu6050_i2c_sda_unlock               
00004531  mpu_configure_fifo                   
00005265  mpu_get_accel_fsr                    
000058d5  mpu_get_gyro_fsr                     
0000683d  mpu_get_sample_rate                  
00003265  mpu_init                             
0000338d  mpu_load_firmware                    
000039f9  mpu_lp_accel_mode                    
000037ed  mpu_read_fifo_stream                 
00004759  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00003dc5  mpu_set_accel_fsr                    
00002381  mpu_set_bypass                       
000045ed  mpu_set_dmp_state                    
000043a9  mpu_set_gyro_fsr                     
00004955  mpu_set_int_latched                  
000042d9  mpu_set_lpf                          
00003bf5  mpu_set_sample_rate                  
0000300d  mpu_set_sensors                      
00004805  mpu_write_mem                        
00002da5  mspm0_i2c_read                       
0000446d  mspm0_i2c_write                      
00002ed9  qsort                                
2020040c  quat                                 
000054f1  read_encoder                         
000085a8  reg                                  
00003f85  scalbn                               
00003f85  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
000029b5  sqrt                                 
000029b5  sqrtl                                
2020044a  stop_time_cnt                        
00008580  test                                 
2020044c  time                                 
202004b4  uwTick                               
000063b1  vsnprintf                            
000076b5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021d1  Task_Start                           
00002381  mpu_set_bypass                       
00002521  __aeabi_dsub                         
00002521  __subdf3                             
0000252b  __adddf3                             
0000252b  __aeabi_dadd                         
000026b3  __aeabi_idiv0                        
000026b5  atan2                                
000026b5  atan2l                               
0000283d  dmp_set_orientation                  
000029b5  sqrt                                 
000029b5  sqrtl                                
00002b25  MPU6050_Init                         
00002da5  mspm0_i2c_read                       
00002ed9  qsort                                
0000300d  mpu_set_sensors                      
0000313d  SysTick_Handler                      
00003265  mpu_init                             
0000338d  mpu_load_firmware                    
000035d1  OLED_Init                            
000036e1  __aeabi_ddiv                         
000036e1  __divdf3                             
000037ed  mpu_read_fifo_stream                 
000038f5  DL_Timer_initFourCCPWMMode           
000039f9  mpu_lp_accel_mode                    
00003af9  VelocityRing_PID_Realize             
00003bf5  mpu_set_sample_rate                  
00003ce1  __aeabi_dmul                         
00003ce1  __muldf3                             
00003dc5  mpu_set_accel_fsr                    
00003ea9  DL_SYSCTL_configSYSPLL               
00003f85  ldexp                                
00003f85  ldexpl                               
00003f85  scalbn                               
00003f85  scalbnl                              
0000405d  __aeabi_fsub                         
0000405d  __subsf3                             
00004067  __addsf3                             
00004067  __aeabi_fadd                         
00004209  GROUP1_IRQHandler                    
000042d9  mpu_set_lpf                          
000043a9  mpu_set_gyro_fsr                     
0000446d  mspm0_i2c_write                      
00004531  mpu_configure_fifo                   
000045ed  mpu_set_dmp_state                    
000046a5  Task_Add                             
00004759  mpu_read_mem                         
00004805  mpu_write_mem                        
000048b1  __udivmoddi4                         
00004953  __aeabi_ldiv0                        
00004955  mpu_set_int_latched                  
000049f1  I2C_OLED_WR_Byte                     
00004a89  dmp_set_fifo_rate                    
00004bb9  SYSCFG_DL_MotorFront_init            
00004c45  SYSCFG_DL_initPower                  
00004cd1  __aeabi_fmul                         
00004cd1  __mulsf3                             
00004de9  Load_Motor_PWM                       
00004e6d  MyPrintf                             
00004ef1  SYSCFG_DL_UART0_init                 
00004ff9  __aeabi_fdiv                         
00004ff9  __divsf3                             
0000507d  LocationRing_PID_Realize             
000050f9  __TI_decompress_lzss                 
00005175  __gedf2                              
00005175  __gtdf2                              
000051e9  __aeabi_errno_addr                   
000051f1  __aeabi_d2f                          
000051f1  __truncdfsf2                         
00005265  mpu_get_accel_fsr                    
000052d9  Motor_SetPWM                         
00005349  LocationRing_Out                     
000053b5  I2C_OLED_Clear                       
00005421  __cmpdf2                             
00005421  __eqdf2                              
00005421  __ledf2                              
00005421  __ltdf2                              
00005421  __nedf2                              
00005489  main                                 
000054f1  read_encoder                         
000055bf  dmp_set_tap_axes                     
00005625  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005689  SYSCFG_DL_I2C_OLED_init              
000056ed  __aeabi_dcmpeq                       
00005701  __aeabi_dcmplt                       
00005715  __aeabi_dcmple                       
00005729  __aeabi_dcmpge                       
0000573d  __aeabi_dcmpgt                       
00005751  __aeabi_fcmpeq                       
00005765  __aeabi_fcmplt                       
00005779  __aeabi_fcmple                       
0000578d  __aeabi_fcmpge                       
000057a1  __aeabi_fcmpgt                       
000057b5  I2C_OLED_i2c_sda_unlock              
00005815  dmp_enable_gyro_cal                  
00005875  mpu6050_i2c_sda_unlock               
000058d5  mpu_get_gyro_fsr                     
00005935  DL_I2C_fillControllerTXFIFO          
00005995  Motor_SetDirc                        
000059f1  SYSCFG_DL_SYSCTL_init                
00005a4d  frexp                                
00005a4d  frexpl                               
00005b61  SYSCFG_DL_I2C_MPU6050_init           
00005bb9  Serial_Init                          
00005c11  Task_IdleFunction                    
00005c69  __TI_ltoa                            
00005d19  __aeabi_idiv                         
00005d19  __aeabi_idivmod                      
00005d71  Task_Encoder                         
00005dc5  Task_Init                            
00005f5d  DL_DMA_initChannel                   
00005ff5  __aeabi_d2iz                         
00005ff5  __fixdfsi                            
00006041  DL_UART_init                         
00006089  dmp_enable_6x_lp_quat                
000060d1  dmp_enable_lp_quat                   
00006161  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000061a5  dmp_set_shake_reject_thresh          
000061e9  dmp_set_tap_count                    
00006271  PID_Param_Init                       
000062b1  VelocityRing_Out                     
000062f1  __aeabi_uidiv                        
000062f1  __aeabi_uidivmod                     
00006331  __aeabi_f2d                          
00006331  __extendsfdf2                        
00006371  atoi                                 
000063b1  vsnprintf                            
00006521  DL_I2C_flushControllerTXFIFO         
00006599  __aeabi_i2f                          
00006599  __floatsisf                          
000065d5  __gesf2                              
000065d5  __gtsf2                              
00006611  __TI_auto_init_nobinit_nopinit       
0000664d  __cmpsf2                             
0000664d  __eqsf2                              
0000664d  __lesf2                              
0000664d  __ltsf2                              
0000664d  __nesf2                              
00006689  __muldsi3                            
000066c5  Motor_Start                          
000066fd  Task_Serial                          
00006735  __aeabi_f2iz                         
00006735  __fixsfsi                            
00006809  SYSCFG_DL_init                       
0000683d  mpu_get_sample_rate                  
00006871  dmp_set_shake_reject_time            
000068a3  dmp_set_shake_reject_timeout         
00006905  Interrupt_Init                       
00006935  SYSCFG_DL_DMA_CH_RX_init             
000069c5  dmp_set_tap_time                     
000069f5  dmp_set_tap_time_multi               
00006a51  __aeabi_i2d                          
00006a51  __floatsidf                          
00006be5  SysTick_Increasment                  
00006c0d  __aeabi_ui2f                         
00006c0d  __floatunsisf                        
00006c35  _c_int00_noargs                      
00006ccf  DL_I2C_setClockConfig                
00006d19  LocationRing_VelocityRing_Control    
00006d3d  __aeabi_lmul                         
00006d3d  __muldi3                             
00006d61  memccpy                              
00006dc5  DL_UART_transmitDataBlocking         
00006de5  Delay                                
00006e05  memcmp                               
00006e45  __aeabi_llsl                         
00006e45  __ashldi3                            
00007025  DL_Timer_setCaptCompUpdateMethod     
00007041  DL_Timer_setClockConfig              
00007079  dmp_load_motion_driver_firmware      
000073c5  DL_Timer_setCaptureCompareOutCtl     
0000746d  SYSCFG_DL_DMA_CH_TX_init             
0000750b  SysGetTick                           
000075fd  __aeabi_uldivmod                     
00007611  dmp_register_android_orient_cb       
00007625  dmp_register_tap_cb                  
0000764d  DL_UART_setClockConfig               
0000765f  TI_memcpy_small                      
00007671  __TI_decompress_none                 
00007695  DL_Timer_setCaptureCompareValue      
000076a5  SYSCFG_DL_SYSTICK_init               
000076b5  wcslen                               
000076c5  __TI_zero_init                       
000076d5  __aeabi_memset                       
000076d5  __aeabi_memset4                      
000076d5  __aeabi_memset8                      
000076ff  TI_memset_small                      
0000770d  SYSCFG_DL_DMA_init                   
00007719  Sys_GetTick                          
00007725  __aeabi_memclr                       
00007725  __aeabi_memclr4                      
00007725  __aeabi_memclr8                      
00007731  DL_Common_delayCycles                
000077bd  __aeabi_memcpy                       
000077bd  __aeabi_memcpy4                      
000077bd  __aeabi_memcpy8                      
000077e3  abort                                
000077e9  ADC0_IRQHandler                      
000077e9  ADC1_IRQHandler                      
000077e9  AES_IRQHandler                       
000077e9  CANFD0_IRQHandler                    
000077e9  DAC0_IRQHandler                      
000077e9  DMA_IRQHandler                       
000077e9  Default_Handler                      
000077e9  GROUP0_IRQHandler                    
000077e9  HardFault_Handler                    
000077e9  I2C0_IRQHandler                      
000077e9  I2C1_IRQHandler                      
000077e9  NMI_Handler                          
000077e9  PendSV_Handler                       
000077e9  RTC_IRQHandler                       
000077e9  SPI0_IRQHandler                      
000077e9  SPI1_IRQHandler                      
000077e9  SVC_Handler                          
000077e9  TIMA0_IRQHandler                     
000077e9  TIMA1_IRQHandler                     
000077e9  TIMG0_IRQHandler                     
000077e9  TIMG12_IRQHandler                    
000077e9  TIMG6_IRQHandler                     
000077e9  TIMG7_IRQHandler                     
000077e9  TIMG8_IRQHandler                     
000077e9  UART0_IRQHandler                     
000077e9  UART1_IRQHandler                     
000077e9  UART2_IRQHandler                     
000077e9  UART3_IRQHandler                     
000077ec  C$$EXIT                              
000077ed  HOSTexit                             
000077f5  Reset_Handler                        
00007809  _system_pre_init                     
00008410  __aeabi_ctype_table_                 
00008410  __aeabi_ctype_table_C                
00008580  test                                 
000085a8  reg                                  
00008628  hw                                   
0000866c  __TI_Handler_Table_Base              
00008678  __TI_Handler_Table_Limit             
00008680  __TI_CINIT_Base                      
00008690  __TI_CINIT_Limit                     
00008690  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004a3  Flag_MPU6050_Ready                   
202004a4  Data_MotorEncoder                    
202004a8  __aeabi_errno                        
202004ac  delayTick                            
202004b4  uwTick                               
202004ba  Task_Flag                            
202004bc  Task_State                           
202004bd  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[300 symbols]
